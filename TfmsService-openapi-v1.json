{"openapi": "3.0.1", "info": {"title": "Extended Fleet Services", "description": "Extended Fleet Services API", "version": "v1"}, "paths": {"/audit/users/{id}": {"get": {"tags": ["Audit"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUserHistory.GetClientUserHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUserHistory.GetClientUserHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUserHistory.GetClientUserHistoryResponse"}}}}}}}, "/audit/users": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryResponse"}}}}}}}, "/audit/issuance/settings/purpose": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryResponse"}}}}}}}, "/audit/issuance/settings/rule": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryResponse"}}}}}}}, "/audit/issuance/settings/vehicle-categories": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryResponse"}}}}}}}, "/audit/login/{clientUserId}": {"get": {"tags": ["Audit"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "loginMode", "in": "query", "schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginMode"}}, {"name": "loginResult", "in": "query", "schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginResult"}}, {"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginHistory.GetLoginHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginHistory.GetLoginHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginHistory.GetLoginHistoryResponse"}}}}}}}, "/audit/login": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryResponse"}}}}}}}, "/audit/vehicles/{id}": {"get": {"tags": ["Audit"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "From", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "To", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehicleHistory.GetVehicleHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehicleHistory.GetVehicleHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehicleHistory.GetVehicleHistoryResponse"}}}}}}}, "/audit/vehicles": {"post": {"tags": ["Audit"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryResponse"}}}}}}}, "/auth/public-key": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.Login.LoginResponse"}}}}}}}, "/auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenResponse"}}}}}}}, "/auth/revoke-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Auth.IO.Http.RevokeTokenResponse"}}}}}}}, "/booking/additionalLocations": {"get": {"tags": ["Booking"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetAdditionalLocations.GetAdditionalLocationsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetAdditionalLocations.GetAdditionalLocationsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetAdditionalLocations.GetAdditionalLocationsResponse"}}}}}}}, "/booking/cancel-booking-reasons": {"get": {"tags": ["Booking"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetCancelBookingReasons.GetCancelBookingReasonsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetCancelBookingReasons.GetCancelBookingReasonsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetCancelBookingReasons.GetCancelBookingReasonsResponse"}}}}}}}, "/booking/force-terminate-booking-reasons": {"get": {"tags": ["Booking"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons.GetForceTerminateBookingReasonsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons.GetForceTerminateBookingReasonsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons.GetForceTerminateBookingReasonsResponse"}}}}}}}, "/booking/reject-booking-reasons": {"get": {"tags": ["Booking"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRejectBookingReasons.GetRejectBookingReasonsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRejectBookingReasons.GetRejectBookingReasonsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRejectBookingReasons.GetRejectBookingReasonsResponse"}}}}}}}, "/booking/requestpurposes": {"get": {"tags": ["Booking"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposes.GetRequestPurposesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposes.GetRequestPurposesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposes.GetRequestPurposesResponse"}}}}}}}, "/booking/requestpurpose-vehicle-categories-map": {"get": {"tags": ["Booking"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap.GetRequestPurposeVehicleCategoriesMapResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap.GetRequestPurposeVehicleCategoriesMapResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap.GetRequestPurposeVehicleCategoriesMapResponse"}}}}}}}, "/": {"get": {"tags": ["Cartrack.Fleet.Host"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/driver/departments/{clientDriverId}": {"get": {"tags": ["Driver"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDepartments.GetDepartmentsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDepartments.GetDepartmentsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDepartments.GetDepartmentsResponse"}}}}}}}, "/driver/{clientDriverId}": {"get": {"tags": ["Driver"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriver.GetDriverResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriver.GetDriverResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriver.GetDriverResponse"}}}}}}, "patch": {"tags": ["Driver"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverResponse"}}}}}}}, "/driver/clientUserId/{clientUserId}": {"get": {"tags": ["Driver"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriverByClientUserId.GetDriverByClientUserIdResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriverByClientUserId.GetDriverByClientUserIdResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriverByClientUserId.GetDriverByClientUserIdResponse"}}}}}}}, "/driver": {"get": {"tags": ["Driver"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDrivers.GetDriversResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDrivers.GetDriversResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDrivers.GetDriversResponse"}}}}}}}, "/driver/active": {"get": {"tags": ["Driver"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriversByUserId.GetDriversByUserIdResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriversByUserId.GetDriversByUserIdResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetDriversByUserId.GetDriversByUserIdResponse"}}}}}}}, "/driver/pdplicenses/{clientDriverId}": {"get": {"tags": ["Driver"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetPdpLicenses.GetPdpLicensesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetPdpLicenses.GetPdpLicensesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetPdpLicenses.GetPdpLicensesResponse"}}}}}}}, "/driver/qdllicenses/{clientDriverId}": {"get": {"tags": ["Driver"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetQdlLicenses.GetQdlLicensesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetQdlLicenses.GetQdlLicensesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Features.GetQdlLicenses.GetQdlLicensesResponse"}}}}}}}, "/file/copy": {"put": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyResponse"}}}}}}}, "/file/copyBulk": {"put": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkResponse"}}}}}}}, "/file/delete": {"delete": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteResponse"}}}}}}}, "/file/deleteBulk": {"delete": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkResponse"}}}}}}}, "/file/download-many": {"post": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDownloadBulk.FileDownloadBulkRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDownloadBulk.FileDownloadBulkRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDownloadBulk.FileDownloadBulkRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/file/info": {"get": {"tags": ["Files"], "parameters": [{"name": "target", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoResponse"}}}}}}}, "/file/move": {"put": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveResponse"}}}}}}}, "/file/moveBulk": {"put": {"tags": ["Files"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkResponse"}}}}}}}, "/file/upload": {"post": {"tags": ["Files"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"TargetFile": {"type": "string"}, "SourceFile": {"type": "string", "format": "binary"}, "Overwrite": {"type": "boolean"}}}, "encoding": {"TargetFile": {"style": "form"}, "SourceFile": {"style": "form"}, "Overwrite": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadResponse"}}}}}}}, "/file/upload-many": {"post": {"tags": ["Files"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"metadata": {"type": "string"}, "files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"metadata": {"style": "form"}, "files": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUploadBulk.FileUploadBulkResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUploadBulk.FileUploadBulkResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUploadBulk.FileUploadBulkResponse"}}}}}}}, "/folder/getfiles": {"get": {"tags": ["Folder"], "parameters": [{"name": "path", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "searchOption", "in": "query", "schema": {"type": "string", "default": "TopDirectoryOnly"}}, {"name": "searchPattern", "in": "query", "schema": {"type": "string", "default": "*"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesResponse"}}}}}}}, "/job/{id}": {"get": {"tags": ["Jobs"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.GetJobInfo.GetJobInfoResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.GetJobInfo.GetJobInfoResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.GetJobInfo.GetJobInfoResponse"}}}}}}}, "/license/pdp/{clientDriverId}": {"get": {"tags": ["License"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetPdpLicense.GetPdpLicenseResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetPdpLicense.GetPdpLicenseResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetPdpLicense.GetPdpLicenseResponse"}}}}}}}, "/license/qdl/{clientDriverId}": {"get": {"tags": ["License"], "parameters": [{"name": "clientDriverId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetQdlLicense.GetQdlLicenseResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetQdlLicense.GetQdlLicenseResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetQdlLicense.GetQdlLicenseResponse"}}}}}}}, "/license/ismatching": {"post": {"tags": ["License"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingResponse"}}}}}}}, "/license/validate": {"post": {"tags": ["License"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesResponse"}}}}}}}, "/logs": {"get": {"tags": ["Logs"], "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetLogs.GetLogsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetLogs.GetLogsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.GetLogs.GetLogsResponse"}}}}}}}, "/scdf/accessory": {"post": {"tags": ["ScdfAccessory"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryResponse"}}}}}}, "get": {"tags": ["ScdfAccessory"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf.ScdfGetAccessoriesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf.ScdfGetAccessoriesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf.ScdfGetAccessoriesResponse"}}}}}}}, "/scdf/accessory/{id}": {"get": {"tags": ["ScdfAccessory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf.ScdfGetAccessoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf.ScdfGetAccessoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf.ScdfGetAccessoryResponse"}}}}}}}, "/scdf/booking/activate": {"patch": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingResponse"}}}}}}}, "/scdf/booking/{bookingId}/approve": {"patch": {"tags": ["ScdfBooking"], "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingResponse"}}}}}}}, "/scdf/booking/cancel": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingResponse"}}}}}}}, "/scdf/booking/create": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingResponse"}}}}}}}, "/scdf/booking/deleteTempSingleImage": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingResponse"}}}}}}}, "/scdf/booking/completed": {"patch": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingResponse"}}}}}}}, "/scdf/booking/forceTerminate": {"patch": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingResponse"}}}}}}}, "/scdf/booking/{id}": {"get": {"tags": ["ScdfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Scdf.ScdfGetBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Scdf.ScdfGetBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Scdf.ScdfGetBookingResponse"}}}}}}}, "/scdf/booking/{bookingId}/activity-logs": {"get": {"tags": ["ScdfBooking"], "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf.ScdfGetBookingActivityLogsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf.ScdfGetBookingActivityLogsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf.ScdfGetBookingActivityLogsResponse"}}}}}}}, "/scdf/booking/{id}/attachments": {"get": {"tags": ["ScdfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf.ScdfGetBookingAttachmentsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf.ScdfGetBookingAttachmentsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf.ScdfGetBookingAttachmentsResponse"}}}}}}}, "/scdf/booking": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ServerRequestModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ServerRequestModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ServerRequestModel"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfGetBookingsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfGetBookingsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfGetBookingsResponse"}}}}}}}, "/scdf/booking/process-activity-logs": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsResponse"}}}}}}}, "/scdf/booking/reject": {"patch": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingResponse"}}}}}}}, "/scdf/booking/update/{bookingId}": {"patch": {"tags": ["ScdfBooking"], "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingResponse"}}}}}}}, "/scdf/booking/uploadSingleImageTemp": {"post": {"tags": ["ScdfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingResponse"}}}}}}}, "/scdf/vehicle/{vehicleId}/allowed-drivers": {"get": {"tags": ["ScdfVehicle"], "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf.GetAllowedDriversForVehicleResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf.GetAllowedDriversForVehicleResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf.GetAllowedDriversForVehicleResponse"}}}}}}}, "/scdf/vehicle/vehicle-for-driver": {"get": {"tags": ["ScdfVehicle"], "parameters": [{"name": "clientDriverId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf.GetVehicleForDriverResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf.GetVehicleForDriverResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf.GetVehicleForDriverResponse"}}}}}}}, "/spf/booking/{id}/activate": {"post": {"tags": ["SpfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingResponse"}}}}}}}, "/spf/booking/{id}/approve": {"post": {"tags": ["SpfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingResponse"}}}}}}}, "/spf/booking/create": {"post": {"tags": ["SpfBooking"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingResponse"}}}}}}}, "/spf/booking/complete": {"post": {"tags": ["SpfBooking"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingResponse"}}}}}}}, "/spf/booking/{id}": {"get": {"tags": ["SpfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Spf.SpfGetBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Spf.SpfGetBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBooking.Spf.SpfGetBookingResponse"}}}}}}}, "/spf/booking": {"get": {"tags": ["SpfBooking"], "parameters": [{"name": "offset", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Spf.SpfGetBookingsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Spf.SpfGetBookingsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Spf.SpfGetBookingsResponse"}}}}}}}, "/spf/booking/{id}/reject": {"post": {"tags": ["SpfBooking"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingResponse"}}}}}}}, "/spf/booking/update/{bookingId}": {"patch": {"tags": ["SpfBooking"], "parameters": [{"name": "bookingId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingResponse"}}}}}}}, "/user/create": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserResponse"}}}}}}}, "/user/{clientUserId}": {"delete": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.DeleteUser.DeleteUserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.DeleteUser.DeleteUserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.DeleteUser.DeleteUserResponse"}}}}}}}, "/user/getdepartmentbyid/{clientUserId}": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDepartmentsById.GetUserDepartmentResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDepartmentsById.GetUserDepartmentResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDepartmentsById.GetUserDepartmentResponse"}}}}}}}, "/user/{clientUserId}/driver-and-groups-access": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess.GetDriverAndGroupsAccessResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess.GetDriverAndGroupsAccessResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess.GetDriverAndGroupsAccessResponse"}}}}}}}, "/user/{clientUserId}/permissions": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetPermissions.GetPermissionsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetPermissions.GetPermissionsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetPermissions.GetPermissionsResponse"}}}}}}}, "/user/getappsettingsbyid/{clientUserId}": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserAppSettingsById.GetUserAppSettingsByIdResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserAppSettingsById.GetUserAppSettingsByIdResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserAppSettingsById.GetUserAppSettingsByIdResponse"}}}}}}}, "/user/getbyid/{clientUserId}": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserById.GetUserByResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserById.GetUserByResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserById.GetUserByResponse"}}}}}}}, "/user/getbyusername": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "userName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserByUsername.GetUserByNameResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserByUsername.GetUserByNameResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUserByUsername.GetUserByNameResponse"}}}}}}}, "/user/{userId}": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsers.GetUserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsers.GetUserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsers.GetUserResponse"}}}}}}}, "/user/getbyrole": {"get": {"tags": ["User"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsersByRole.GetUserByRoleResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsersByRole.GetUserByRoleResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetUsersByRole.GetUserByRoleResponse"}}}}}}}, "/user/{clientUserId}/vehicle-and-groups-access": {"get": {"tags": ["User"], "parameters": [{"name": "clientUserId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess.GetVehicleAndGroupsAccessResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess.GetVehicleAndGroupsAccessResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess.GetVehicleAndGroupsAccessResponse"}}}}}}}, "/user/updatelastlogin": {"patch": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginResponse"}}}}}}}, "/user/updatebyid": {"patch": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.UpdateUser.UpdateUserResponse"}}}}}}}, "/vehicle/id/{id}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64", "default": 0}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleById.GetVehicleByIdResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleById.GetVehicleByIdResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleById.GetVehicleByIdResponse"}}}}}}}, "/vehicle/registration/{id}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "default": "FBB1234X"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration.GetVehicleByRegistrationResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration.GetVehicleByRegistrationResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration.GetVehicleByRegistrationResponse"}}}}}}}, "/vehicle/categories": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleCategories.GetVehicleCategoriesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleCategories.GetVehicleCategoriesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleCategories.GetVehicleCategoriesResponse"}}}}}}}, "/vehicle/departments/{vehicleId}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64", "default": 0}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments.GetVehicleDepartmentsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments.GetVehicleDepartmentsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments.GetVehicleDepartmentsResponse"}}}}}}}, "/vehicle/groups": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleGroups.GetVehicleGroupsResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleGroups.GetVehicleGroupsResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleGroups.GetVehicleGroupsResponse"}}}}}}}, "/vehicle/pdplicenses/{vehicleId}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses.GetVehiclePdpLicensesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses.GetVehiclePdpLicensesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses.GetVehiclePdpLicensesResponse"}}}}}}}, "/vehicle/qdllicenses/{vehicleId}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses.GetVehicleQdlLicensesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses.GetVehicleQdlLicensesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses.GetVehicleQdlLicensesResponse"}}}}}}}, "/vehicle": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicles.GetVehiclesResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicles.GetVehiclesResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehicles.GetVehiclesResponse"}}}}}}}, "/vehicle/category/{vehicleCategoryId}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleCategoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory.GetVehiclesByVehicleCategoryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory.GetVehiclesByVehicleCategoryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory.GetVehiclesByVehicleCategoryResponse"}}}}}}}, "/vehicle/group/{vehicleGroupId}": {"get": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleGroupId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup.GetVehiclesByVehicleGroupResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup.GetVehiclesByVehicleGroupResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup.GetVehiclesByVehicleGroupResponse"}}}}}}}, "/vehicle/{vehicleId}": {"patch": {"tags": ["Vehicle"], "parameters": [{"name": "vehicleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleResponse"}}}}}}}}, "components": {"schemas": {"Cartrack.Core.Auth.Domain.Login": {"required": ["accessToken", "expiresAtUtc", "refreshToken"], "type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresAtUtc": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Cartrack.Core.Auth.Domain.Token": {"required": ["accessToken", "expiresAt", "refreshToken"], "type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Cartrack.Core.Auth.Features.Login.LoginRequest": {"required": ["account", "password", "username"], "type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "account": {"type": "string", "nullable": true}, "apiKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Auth.Features.Login.LoginResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Domain.Login"}, "Success": {"type": "boolean", "readOnly": true}, "passwordVerificationResult": {"$ref": "#/components/schemas/Microsoft.AspNetCore.Identity.PasswordVerificationResult"}}, "additionalProperties": false}, "Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenRequest": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Auth.Features.RefreshToken.RefreshTokenResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Auth.Domain.Token"}, "Success": {"type": "boolean", "readOnly": true}, "isExpired": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Auth.IO.Http.RevokeTokenRequest": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Auth.IO.Http.RevokeTokenResponse": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "Success": {"type": "boolean", "readOnly": true}, "isExpired": {"type": "boolean"}, "isUnauthorized": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileCopy.FileCopyRequest": {"type": "object", "properties": {"sourcePath": {"type": "string", "nullable": true}, "destinationPath": {"type": "string", "nullable": true}, "overwrite": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileCopy.FileCopyResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileCopy.FileCopyStatus": {"type": "object", "properties": {"request": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyRequest"}, "job": {"$ref": "#/components/schemas/Cartrack.Core.Files.IO.Http.DataContracts.JobInfo"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkRequest": {"type": "object", "properties": {"fileCopyRequests": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyRequest"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileCopyBulk.FileCopyBulkResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileCopy.FileCopyStatus"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest": {"type": "object", "properties": {"uriPath": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDelete.FileDeleteResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDelete.FileDeleteStatus": {"type": "object", "properties": {"request": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest"}, "job": {"$ref": "#/components/schemas/Cartrack.Core.Files.IO.Http.DataContracts.JobInfo"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkRequest": {"type": "object", "properties": {"fileDeleteBulkRequest": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteRequest"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDeleteBulk.FileDeleteBulkResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteStatus"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileDelete.FileDeleteStatus"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileDownloadBulk.FileDownloadBulkRequest": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileGetInfo.FileGetInfoStatus": {"type": "object", "properties": {"target": {"type": "string", "nullable": true}, "size": {"type": "string", "nullable": true}, "hash": {"type": "string", "nullable": true}, "createdUtc": {"type": "string", "format": "date-time"}, "modifiedUtc": {"type": "string", "format": "date-time"}, "publicUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileMove.FileMoveRequest": {"type": "object", "properties": {"sourcePath": {"type": "string", "nullable": true}, "destinationPath": {"type": "string", "nullable": true}, "overwrite": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileMove.FileMoveResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileMove.FileMoveStatus": {"type": "object", "properties": {"request": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveRequest"}, "job": {"$ref": "#/components/schemas/Cartrack.Core.Files.IO.Http.DataContracts.JobInfo"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkRequest": {"type": "object", "properties": {"moveBulkRequest": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveRequest"}, "nullable": true}, "owerwrite": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileMoveBulk.FileMoveBulkResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileMove.FileMoveStatus"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileUpload.FileUploadInfo": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "length": {"type": "integer", "format": "int64"}, "destinationPath": {"type": "string", "nullable": true}, "overwrite": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileUpload.FileUploadResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileUpload.FileUploadStatus": {"type": "object", "properties": {"request": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadInfo"}, "job": {"$ref": "#/components/schemas/Cartrack.Core.Files.IO.Http.DataContracts.JobInfo"}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FileUploadBulk.FileUploadBulkResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FileUpload.FileUploadStatus"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.FolderGetFiles.FolderGetFilesStatus": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Core.Files.Features.GetJobInfo.GetJobInfoResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Core.Files.IO.Http.DataContracts.JobInfo"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Core.Files.IO.Http.DataContracts.JobInfo": {"type": "object", "properties": {"jobId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "nullable": true}, "completionLevel": {"type": "integer", "format": "int32"}, "description": {"type": "string", "nullable": true}, "statusDetails": {"type": "string", "nullable": true}, "link": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientAction": {"type": "object", "properties": {"clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "note": {"type": "string", "nullable": true}, "noteTs": {"type": "string", "format": "date-time"}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientDriverNotes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverNote"}, "nullable": true}, "clientDriverStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverState"}, "nullable": true}, "clientDriverVehicles": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverVehicle"}, "nullable": true}, "clientUserToClientDrivers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToClientDriver"}, "nullable": true}, "clientUserToVehicles": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToVehicle"}, "nullable": true}, "clientVehicleNotes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientVehicleNote"}, "nullable": true}, "identificationTagStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagState"}, "nullable": true}, "notificationConfigurationGroupNotes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.NotificationConfigurationGroupNote"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriver": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "driverName": {"type": "string", "nullable": true}, "idNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "gender": {"type": "integer", "format": "int32"}, "cellNumber": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "licenseNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "licenseDriverRestrictions": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "licenseVehicleRestrictions": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "licenseValidStart": {"type": "string", "format": "date-time", "nullable": true}, "licenseValidEnd": {"type": "string", "format": "date-time", "nullable": true}, "licenseCode": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "licenseFirstIssueDate": {"type": "string", "format": "date-time", "nullable": true}, "licenseIssuedCountry": {"maxLength": 2, "minLength": 0, "type": "string", "nullable": true}, "licensePoints": {"type": "integer", "format": "int32", "nullable": true}, "expiryNotificationId": {"type": "integer", "format": "int32", "nullable": true}, "expiryUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "expiryNotificationMethodId": {"type": "integer", "format": "int32", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "homeGeofenceId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "workStartTime": {"type": "string", "nullable": true}, "workEndTime": {"type": "string", "nullable": true}, "cardId": {"type": "string", "nullable": true}, "driverPassword": {"maxLength": 40, "minLength": 0, "type": "string", "nullable": true}, "licenseIssuedCountryState": {"maxLength": 2, "minLength": 0, "type": "string", "nullable": true}, "serviceAccount": {"type": "boolean", "nullable": true}, "exemptFromEld": {"type": "boolean", "nullable": true}, "eldCycleRuleId": {"type": "integer", "format": "int32", "nullable": true}, "driverLoginUsername": {"maxLength": 24, "minLength": 0, "type": "string", "nullable": true}, "eMail": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "driverSurname": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "defaultVehicleId": {"type": "integer", "format": "int64", "nullable": true}, "logoImageBase64": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}, "socialSecurityNumber": {"type": "string", "nullable": true}, "managerName": {"type": "string", "nullable": true}, "hiredDate": {"type": "string", "format": "date-time", "nullable": true}, "terminationDate": {"type": "string", "format": "date-time", "nullable": true}, "departmentHead": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int64", "nullable": true}, "laborRate": {"type": "number", "format": "double", "nullable": true}, "billingRate": {"type": "number", "format": "double", "nullable": true}, "monthlyWageCost": {"type": "number", "format": "double", "nullable": true}, "unit": {"type": "string", "nullable": true}, "isEncrypted": {"type": "boolean", "nullable": true}, "isDeleted": {"type": "boolean", "nullable": true}, "email1": {"type": "string", "nullable": true}, "clientDriverAddresses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverAddress"}, "nullable": true}, "clientDriverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverLicense"}, "nullable": true}, "clientDriverNotes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverNote"}, "nullable": true}, "clientDriverStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverState"}, "nullable": true}, "clientDriverTags": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverTag"}, "nullable": true}, "clientDriverVehicles": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverVehicle"}, "nullable": true}, "clientUserToClientDrivers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToClientDriver"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverAddress": {"type": "object", "properties": {"clientDriverAddressId": {"type": "integer", "format": "int64"}, "clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "street": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "timezone": {"type": "integer", "format": "int32", "nullable": true}, "addressTypeId": {"type": "integer", "format": "int32", "nullable": true}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverLicense": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "driverLicenseTypeId": {"type": "integer", "format": "int64"}, "licenseNumber": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "licenseValidStart": {"type": "string", "format": "date-time", "nullable": true}, "licenseValidEnd": {"type": "string", "format": "date-time", "nullable": true}, "licenseFirstIssueDate": {"type": "string", "format": "date-time", "nullable": true}, "licenseIssuedCountry": {"maxLength": 2, "minLength": 0, "type": "string", "nullable": true}, "licensePoints": {"type": "integer", "format": "int32", "nullable": true}, "licenseDriverRestrictions": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "suspensionStartDate": {"type": "string", "format": "date-time", "nullable": true}, "suspensionEndDate": {"type": "string", "format": "date-time", "nullable": true}, "disqualificationStartDate": {"type": "string", "format": "date-time", "nullable": true}, "disqualificationEndDate": {"type": "string", "format": "date-time", "nullable": true}, "revocationStartDate": {"type": "string", "format": "date-time", "nullable": true}, "revocationEndDate": {"type": "string", "format": "date-time", "nullable": true}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}, "driverLicenseType": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.DriverLicenseType"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverNote": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverState": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientDriverStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}, "clientDriverStatus": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverStatus"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverStatus": {"type": "object", "properties": {"clientDriverStatusId": {"type": "integer", "format": "int32"}, "statusDescription": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "clientDriverStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverState"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverTag": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "identificationTagId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "assignedTs": {"type": "string", "format": "date-time"}, "unassignedTs": {"type": "string", "format": "date-time"}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientDriverTagId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientDriverTagDescription": {"type": "string", "nullable": true}, "lost": {"type": "boolean", "nullable": true}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}, "identificationTag": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTag"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientDriverVehicle": {"type": "object", "properties": {"clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64"}, "relationshipStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientUser": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "passwordHash": {"maxLength": 64, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "cellNumber": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "eMail": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isLocked": {"type": "boolean"}, "isRole": {"type": "boolean"}, "clientUserRoleId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentClientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientRoleId": {"type": "integer", "format": "int64", "nullable": true}, "isDeleted": {"type": "boolean", "nullable": true}, "forceRoleSetting": {"type": "boolean", "nullable": true}, "clientUserToClientDrivers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToClientDriver"}, "nullable": true}, "clientUserToVehicles": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToVehicle"}, "nullable": true}, "clientUserTokens": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserToken"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientUserDepartment": {"type": "object", "properties": {"clientUserDepartmentId": {"type": "integer", "format": "int64"}, "clientUserId": {"type": "string", "nullable": true}, "departmentId": {"type": "integer", "format": "int64", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientUserToClientDriver": {"type": "object", "properties": {"clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientDriverId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "relationshipStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "clientDriver": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriver"}, "clientUser": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientUserToVehicle": {"type": "object", "properties": {"clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64"}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "relationshipStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "clientUser": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientUserToken": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "token": {"type": "string", "nullable": true}, "expiresTs": {"type": "string", "format": "date-time"}, "createdTs": {"type": "string", "format": "date-time"}, "revokedTs": {"type": "string", "format": "date-time", "nullable": true}, "replacedByToken": {"type": "string", "nullable": true}, "clientUserId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "clientUser": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.ClientVehicleNote": {"type": "object", "properties": {"vehicleId": {"type": "integer", "format": "int64"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "vehicle": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.VehicleAdditionalInfo"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.DriverLicenseType": {"type": "object", "properties": {"driverLicenseTypeId": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "name": {"type": "string", "nullable": true}, "clientDriverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.IdentificationTag": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "identificationTagId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "identificationTag1": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "firstSeenTs": {"type": "string", "format": "date-time", "nullable": true}, "identificationTagStatusId": {"type": "integer", "format": "int32"}, "privacyMode": {"type": "boolean", "nullable": true}, "clientDriverTags": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientDriverTag"}, "nullable": true}, "identificationTagStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagState"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.IdentificationTagState": {"type": "object", "properties": {"identificationTagId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "identificationTagStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}, "identificationTag": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTag"}, "identificationTagStateHists": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagStateHist"}, "nullable": true}, "identificationTagStatus": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagStatus"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.IdentificationTagStateHist": {"type": "object", "properties": {"identificationTagStateHistId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "identificationTagId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "identificationTagStatusId": {"type": "integer", "format": "int32"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "identificationTagState": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagState"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.IdentificationTagStatus": {"type": "object", "properties": {"identificationTagStatusId": {"type": "integer", "format": "int32"}, "statusDescription": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "identificationTagStates": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.IdentificationTagState"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.NotificationConfigurationGroupNote": {"type": "object", "properties": {"notificationConfigurationGroupId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "clientAction": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientAction"}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.VehicleAdditionalInfo": {"type": "object", "properties": {"vehicleId": {"type": "integer", "format": "int64"}, "clientVehicleDescription": {"maxLength": 64, "minLength": 0, "type": "string", "nullable": true}, "cts": {"type": "string", "format": "date-time", "nullable": true}, "uts": {"type": "string", "format": "date-time", "nullable": true}, "serviceIntervalOdometer": {"type": "integer", "format": "int64", "nullable": true}, "serviceIntervalWorkingHours": {"type": "integer", "format": "int64", "nullable": true}, "fuelCapacity": {"type": "integer", "format": "int32", "nullable": true}, "homeGeofence": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "licenceCode": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "licenceIssuedDate": {"type": "string", "format": "date-time", "nullable": true}, "licenceExpiryDate": {"type": "string", "format": "date-time", "nullable": true}, "licenceValid": {"type": "boolean", "nullable": true}, "defaultDriver": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "fuelLowerRaw": {"type": "integer", "format": "int32", "nullable": true}, "fuelHigherRaw": {"type": "integer", "format": "int32", "nullable": true}, "monthlyMileageLimit": {"type": "integer", "format": "int32", "nullable": true}, "fuelLowerRawAux": {"type": "integer", "format": "int32", "nullable": true}, "fuelHigherRawAux": {"type": "integer", "format": "int32", "nullable": true}, "fuelCapacityAux": {"type": "integer", "format": "int32", "nullable": true}, "tmpFuelPl": {"type": "number", "format": "double", "nullable": true}, "fuelLowerRawFixed": {"type": "integer", "format": "int32", "nullable": true}, "fuelHigherRawFixed": {"type": "integer", "format": "int32", "nullable": true}, "serviceDueDate": {"type": "string", "format": "date-time", "nullable": true}, "clientVehicleDescription1": {"maxLength": 64, "minLength": 0, "type": "string", "nullable": true}, "clientVehicleDescription2": {"maxLength": 64, "minLength": 0, "type": "string", "nullable": true}, "serviceDueOdo": {"type": "integer", "format": "int32", "nullable": true}, "serviceDueType": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "ignOnOdoInvalid": {"type": "boolean", "nullable": true}, "fuelSnRatio": {"type": "integer", "format": "int32", "nullable": true}, "inhibit": {"type": "boolean", "nullable": true}, "tollCategoryId": {"type": "integer", "format": "int32", "nullable": true}, "pinCode": {"type": "string", "nullable": true}, "tankType": {"type": "integer", "format": "int32"}, "defaultTimezone": {"type": "integer", "format": "int32", "nullable": true}, "fuelAlertPct": {"type": "integer", "format": "int32"}, "maxSpeed": {"type": "integer", "format": "int32", "nullable": true}, "tollingTagId": {"maxLength": 48, "minLength": 0, "type": "string", "nullable": true}, "privacyModeEnabled": {"type": "boolean", "nullable": true}, "fuelEconomy": {"type": "number", "format": "double", "nullable": true}, "hubometer": {"maxLength": 32, "minLength": 0, "type": "string", "nullable": true}, "assignTripsToDefaultDriver": {"type": "boolean", "nullable": true}, "maxSpeedUpdateTs": {"type": "string", "format": "date-time", "nullable": true}, "updateTs": {"type": "string", "format": "date-time", "nullable": true}, "odometer": {"type": "integer", "format": "int64", "nullable": true}, "vehicleName": {"maxLength": 64, "minLength": 0, "type": "string", "nullable": true}, "isIgnitionless": {"type": "boolean", "nullable": true}, "fuelAvgConsumptionCombined": {"type": "number", "format": "double", "nullable": true}, "fuelTargetConsumption": {"type": "number", "format": "double", "nullable": true}, "vehicleFuelTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleEngineTypeId": {"type": "integer", "format": "int64", "nullable": true}, "maintenanceId": {"type": "integer", "format": "int32", "nullable": true}, "driverLicenseTypeId": {"type": "integer", "format": "int64", "nullable": true}, "defaultSiteLocationId": {"type": "integer", "format": "int64", "nullable": true}, "maintenanceStatusId": {"type": "integer", "format": "int64"}, "isPoolActive": {"type": "boolean"}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleGearboxTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleWorkCounterTypeId": {"type": "integer", "format": "int64", "nullable": true}, "engineCapacity": {"type": "integer", "format": "int32", "nullable": true}, "departmentId": {"type": "integer", "format": "int64", "nullable": true}, "requiredSpecialLicense": {"type": "string", "nullable": true}, "vehicleStatusOptionId": {"type": "integer", "format": "int64"}, "bookingAllocationPriority": {"type": "number", "format": "double"}, "fuelCalibrationStatusId": {"type": "integer", "format": "int64", "nullable": true}, "fuelCalibrationUpdatedTs": {"type": "string", "format": "date-time", "nullable": true}, "fuelCalibrationUserName": {"type": "string", "nullable": true}, "commonPool": {"type": "boolean"}, "speedSourceId": {"type": "integer", "format": "int32", "nullable": true}, "ticketNumber": {"type": "string", "nullable": true}, "fuelCapacityAux2": {"type": "integer", "format": "int32", "nullable": true}, "fuelHigherRawAux2": {"type": "integer", "format": "int32", "nullable": true}, "fuelLowerRawAux2": {"type": "integer", "format": "int32", "nullable": true}, "fuelAvgConsumptionCalc": {"type": "number", "format": "double", "nullable": true}, "fuelAvgConsumptionCalcTs": {"type": "string", "format": "date-time", "nullable": true}, "secondsToStationary": {"type": "integer", "format": "int32", "nullable": true}, "clientVehicleNotes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientVehicleNote"}, "nullable": true}, "vehicleServiceLogs": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.VehicleServiceLog"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.EFCore.Models.Fleet.VehicleServiceLog": {"type": "object", "properties": {"vehicleServiceLogId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "vehicleId": {"type": "integer", "format": "int64"}, "clientActionId": {"maxLength": 36, "minLength": 0, "type": "string", "nullable": true}, "odometer": {"type": "integer", "format": "int64", "nullable": true}, "workingHours": {"type": "integer", "format": "int64", "nullable": true}, "serviceDate": {"type": "string", "format": "date-time", "nullable": true}, "serviceEnter": {"type": "string", "format": "date-time", "nullable": true}, "serviceExit": {"type": "string", "format": "date-time", "nullable": true}, "vehicle": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.VehicleAdditionalInfo"}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryRequest": {"required": ["name"], "type": "object", "properties": {"account": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "accessoryTypeId": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.Features.CreateAccessory.Scdf.ScdfCreateAccessoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.IO.Http.Accessory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.Features.GetAccessories.Scdf.ScdfGetAccessoriesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.IO.Http.Accessories"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.Features.GetAccessory.Scdf.ScdfGetAccessoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.IO.Http.Accessory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.IO.Http.Accessories": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Accessory.IO.Http.Accessory"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Accessory.IO.Http.Accessory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "accessoryTypeId": {"type": "integer", "format": "int32"}, "accessoryTypeName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.ClientUserChangeHistory": {"required": ["clientUserId", "userId", "userName"], "type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "userId": {"type": "integer", "format": "int64"}, "clientUserId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ClientUserChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.ClientUsersChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ClientUserChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.IssuanceSettingsPurposeChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.IssuanceSettingsPurposeChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.IssuanceSettingsRulesChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.IssuanceSettingsRulesChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.IssuanceSettingsVehicleCategoriesChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.IssuanceSettingsVehicleCategoriesChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.LoginChangeHistory": {"required": ["clientUserId", "clientUserName", "userId"], "type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "userId": {"type": "integer", "format": "int64"}, "clientUserId": {"type": "string", "nullable": true}, "clientUserName": {"type": "string", "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.LoginsChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.VehicleChangeHistory": {"required": ["vehicleId", "vehicleRegistration"], "type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "vehicleId": {"type": "integer", "format": "int64"}, "vehicleRegistration": {"type": "string", "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.VehicleChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Domain.VehiclesChangeHistory": {"type": "object", "properties": {"from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "entity": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.EntityType"}, "totalCount": {"type": "integer", "format": "int32"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.VehicleChange"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetClientUserHistory.GetClientUserHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.ClientUserChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetClientUsersHistory.GetClientUsersHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.ClientUsersChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsPurposeHistory.GetIssuanceSettingsPurposeHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.IssuanceSettingsPurposeChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsRulesHistory.GetIssuanceSettingsRulesHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.IssuanceSettingsRulesChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetIssuanceSettingsVehicleCategoriesHistory.GetIssuanceSettingsVehicleCategoriesHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.IssuanceSettingsVehicleCategoriesChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetLoginHistory.GetLoginHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.LoginChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "loginMode": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginMode"}, "loginResult": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.LoginResult"}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetLoginsHistory.GetLoginsHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.LoginsChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetVehicleHistory.GetVehicleHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.VehicleChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryRequest": {"type": "object", "properties": {"search": {"type": "string", "nullable": true}, "from": {"type": "string", "format": "date-time"}, "to": {"type": "string", "format": "date-time"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Audit.Features.GetVehiclesHistory.GetVehiclesHistoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Audit.Domain.VehiclesChangeHistory"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.ActivityLogAction": {"required": ["statusId"], "type": "object", "properties": {"statusId": {"type": "string", "nullable": true}, "statusName": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "remarks": {"type": "string", "nullable": true}, "vehicleRegistration": {"type": "string", "nullable": true}, "assignedDriver": {"type": "string", "nullable": true}, "assignedVehicleCommander": {"type": "string", "nullable": true}, "pickupDateTime": {"type": "string", "format": "date-time", "nullable": true}, "actualPickupDateTime": {"type": "string", "format": "date-time", "nullable": true}, "dropOffDateTime": {"type": "string", "format": "date-time", "nullable": true}, "actualDropoffDateTime": {"type": "string", "format": "date-time", "nullable": true}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.ActivityLogDataField"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.ActivityLogDataField": {"required": ["field"], "type": "object", "properties": {"field": {"type": "string", "nullable": true}, "previousValue": {"type": "string", "nullable": true}, "updatedValue": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.ActivityLogEntry": {"type": "object", "properties": {"auTimestamp": {"type": "string", "format": "date-time", "nullable": true}, "auClientUser": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.ClientUser"}, "auAction": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.ActivityLogAction"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.CancelBookingReason": {"required": ["description", "id", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.Common.AdditionalLocation": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "locationName": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.Common.ClientUser": {"required": ["clientUserId"], "type": "object", "properties": {"updatedDate": {"type": "string", "format": "date-time", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}, "clientUserId": {"type": "string", "nullable": true}, "passwordHash": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean", "nullable": true}, "email": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "departments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserDepartment"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.Common.DriverType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Booking.Domain.Common.EquipmentType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Booking.Domain.Common.InputEquipmentAttachment": {"type": "object", "properties": {"guid": {"type": "string", "nullable": true}, "extension": {"type": "string", "nullable": true}, "realFilename": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.Common.JourneyLocation": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.Common.JourneyType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Booking.Domain.Common.VehicleCommanderType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Booking.Domain.ForceTerminateBookingReason": {"required": ["description", "id", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.RejectBookingReason": {"required": ["description", "id", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.RequestPurpose": {"required": ["description", "id", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.RequestPurposeVehicleCategoryMap": {"required": ["description", "id", "title"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "vehicleTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.RequestVehicleType"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Domain.RequestVehicleType": {"required": ["id", "name"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingRequest": {"required": ["activatedClientUserId", "clientDriverId", "vehicleCommanderClientUserId"], "type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}, "pickUpTime": {"type": "string", "format": "date-time"}, "actualPickUpTime": {"type": "string", "format": "date-time"}, "vehicleId": {"type": "integer", "format": "int32"}, "clientDriverId": {"type": "string", "nullable": true}, "vehicleCommanderClientUserId": {"type": "string", "nullable": true}, "activatedClientUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ActivateBooking.Scdf.ScdfActivateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingRequest": {"type": "object", "properties": {"account": {"type": "string", "nullable": true}, "bookingId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ActivateBooking.Spf.SpfActivateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingRequest": {"required": ["journeys", "requestClientDriverId"], "type": "object", "properties": {"approveClientUserId": {"type": "string", "nullable": true}, "bookingPurposeId": {"type": "integer", "format": "int64"}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64"}, "isBookingForOtherParty": {"type": "boolean"}, "requestedForClientUserId": {"type": "string", "nullable": true}, "startTs": {"type": "string", "format": "date-time"}, "endTs": {"type": "string", "format": "date-time"}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "requestClientDriverId": {"type": "string", "nullable": true}, "equipmentType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.EquipmentType"}, "accessories": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "remarks": {"type": "string", "nullable": true}, "pickupSiteLocationId": {"type": "integer", "format": "int32"}, "journeyType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyType"}, "journeys": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyLocation"}, "nullable": true}, "vehicleCommanderClientUserId": {"type": "string", "nullable": true}, "numberOfPassengers": {"type": "integer", "format": "int32"}, "equipmentAttachmentIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "equipmentAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.InputEquipmentAttachment"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ApproveBooking.Scdf.ScdfApproveBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingRequest": {"type": "object", "properties": {"account": {"type": "string", "nullable": true}, "bookingId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ApproveBooking.Spf.SpfApproveBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingRequest": {"type": "object", "properties": {"bookingIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "bookingCancelReasonId": {"type": "integer", "format": "int64"}, "bookingCancelNotes": {"type": "string", "nullable": true}, "canceledClientUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CancelBooking.Scdf.ScdfCancelBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Infratructure.BookingResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingRequest": {"required": ["journeys"], "type": "object", "properties": {"bookingPurposeId": {"type": "integer", "format": "int64"}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "requestClientUserId": {"type": "string", "nullable": true}, "requestedForClientUserId": {"type": "string", "nullable": true}, "startTs": {"type": "string", "format": "date-time"}, "endTs": {"type": "string", "format": "date-time"}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "driverType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.DriverType"}, "requestClientDriverId": {"type": "string", "nullable": true}, "equipmentType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.EquipmentType"}, "accessories": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "remarks": {"type": "string", "nullable": true}, "pickupSiteLocationId": {"type": "integer", "format": "int32"}, "journeyType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyType"}, "journeys": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyLocation"}, "nullable": true}, "vehicleCommanderType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.VehicleCommanderType"}, "vehicleCommanderClientUserId": {"type": "string", "nullable": true}, "numberOfPassengers": {"type": "integer", "format": "int32"}, "equipmentAttachmentIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "equipmentAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.InputEquipmentAttachment"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CreateBooking.Scdf.ScdfCreateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingRequest": {"type": "object", "properties": {"bookingPurposeId": {"type": "integer", "format": "int64"}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "requestClientUserId": {"type": "string", "nullable": true}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64", "nullable": true}, "startTs": {"type": "string", "format": "date-time"}, "endTs": {"type": "string", "format": "date-time"}, "requestClientDriverId": {"type": "string", "nullable": true}, "pickupSiteLocationId": {"type": "integer", "format": "int64", "nullable": true}, "returnSiteLocationId": {"type": "integer", "format": "int64", "nullable": true}, "bookingTypeId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.CreateBooking.Spf.SpfCreateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingRequest": {"type": "object", "properties": {"guid": {"type": "string", "nullable": true}, "extension": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.DeleteTempSingleImageBooking.Scdf.ScdfDeleteTempSingleImageBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.TempDeleteImageStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}, "dropOffTime": {"type": "string", "format": "date-time"}, "actualDropOffTime": {"type": "string", "format": "date-time"}, "vehicleId": {"type": "integer", "format": "int64"}, "returnedClientDriverId": {"type": "string", "nullable": true}, "returnedVehicleCommanderClientUserId": {"type": "string", "nullable": true}, "completedClientUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.EndBooking.Scdf.ScdfEndBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.EndBooking.Spf.SpfEndBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingRequest": {"type": "object", "properties": {"bookingIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "bookingForceTerminateReasonId": {"type": "integer", "format": "int32"}, "bookingForceTerminateNotes": {"type": "string", "nullable": true}, "forceTerminateClientUserId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ForceTerminateBooking.Scdf.ScdfForceTerminateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Infratructure.BookingResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetAdditionalLocations.GetAdditionalLocationsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.AdditionalLocations"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBooking.Scdf.ScdfGetBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBooking.Spf.SpfGetBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookingActivityLogs.Scdf.ScdfGetBookingActivityLogsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingActivityLogs"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookingAttachments.Scdf.ScdfGetBookingAttachmentsResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingAttachment"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.FilterItem": {"type": "object", "properties": {"type": {"type": "string", "nullable": true, "readOnly": true}, "field": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.FilterOptions": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.FilterItem"}, "nullable": true}, "logicOperator": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.LogicOperator"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.LogicOperator": {"enum": ["And", "Or"], "type": "string"}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.Pagination": {"type": "object", "properties": {"offset": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}, "pageSize": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.PaginationMetrics": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "statusMetrics": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.StatusCount"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfBooking": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "statusId": {"type": "integer", "format": "int64", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "purpose": {"type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleRegistration": {"type": "string", "nullable": true}, "pickupTime": {"type": "string", "format": "date-time", "nullable": true}, "dropoffTime": {"type": "string", "format": "date-time", "nullable": true}, "type": {"type": "string", "nullable": true}, "requestClientUserId": {"type": "string", "nullable": true}, "requestedBy": {"type": "string", "nullable": true}, "requestedDate": {"type": "string", "format": "date-time", "nullable": true}, "journeys": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Journey"}, "nullable": true}, "pickupLocationId": {"type": "integer", "format": "int64", "nullable": true}, "pickupLocationName": {"type": "string", "nullable": true}, "requestClientDriverId": {"type": "string", "nullable": true}, "driverName": {"type": "string", "nullable": true}, "driverEmail": {"type": "string", "nullable": true}, "remarks": {"type": "string", "nullable": true}, "bookingPurposeId": {"type": "integer", "format": "int64", "nullable": true}, "bookingPurposeTitle": {"type": "string", "nullable": true}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "vehicleCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleCategoryName": {"type": "string", "nullable": true}, "keyReturnTs": {"type": "string", "format": "date-time", "nullable": true}, "keyCollectionTs": {"type": "string", "format": "date-time", "nullable": true}, "accessories": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingAccessory"}, "nullable": true}, "approvedManagers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.DisplayUser"}, "nullable": true}, "rejectedManagers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.DisplayUser"}, "nullable": true}, "commanderClientUserId": {"type": "string", "nullable": true}, "commanderUsername": {"type": "string", "nullable": true}, "requestedForClientUserId": {"type": "string", "nullable": true}, "locationType": {"type": "integer", "format": "int32", "nullable": true}, "driverType": {"type": "integer", "format": "int32", "nullable": true}, "vehicleCommanderType": {"type": "integer", "format": "int32"}, "equipmentType": {"type": "integer", "format": "int32", "nullable": true}, "numberOfPassengers": {"type": "integer", "format": "int32", "nullable": true}, "bookingType": {"type": "integer", "format": "int32"}, "commanderEmail": {"type": "string", "nullable": true}, "requestedByEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfGetBookingsResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ScdfBooking"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}, "metrics": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.PaginationMetrics"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SearchTextFilterItem": {"type": "object", "properties": {"text": {"type": "array", "items": {"type": "string"}, "nullable": true}, "searchText": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.ServerRequestModel": {"type": "object", "properties": {"pagination": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.Pagination"}, "statusIds": {"type": "string", "nullable": true}, "sort": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortOption"}, "filters": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.FilterOptions"}, "quickSearch": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SearchTextFilterItem"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortDirection": {"enum": ["asc", "desc"], "type": "string"}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortField": {"enum": ["requestDate", "pickUpAt", "startDate", "endDate", "returnedAt"], "type": "string"}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortOption": {"type": "object", "properties": {"parsedField": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortField"}, "sort": {"type": "string", "nullable": true}, "field": {"type": "string", "nullable": true}, "parsedSort": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Features.GetBookings.Scdf.SortDirection"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Scdf.StatusCount": {"type": "object", "properties": {"statusId": {"type": "integer", "format": "int64"}, "count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetBookings.Spf.SpfGetBookingsResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetCancelBookingReasons.GetCancelBookingReasonsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.CancelBookingReasons"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetForceTerminateBookingReasons.GetForceTerminateBookingReasonsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.ForceTerminateBookingReasons"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetRejectBookingReasons.GetRejectBookingReasonsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.RejectBookingReasons"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetRequestPurposeVehicleCategoriesMap.GetRequestPurposeVehicleCategoriesMapResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.RequestPurposeVehicleCategoryMaps"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.GetRequestPurposes.GetRequestPurposesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.RequestPurposes"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.ProcessBookingActivityLogs.Scdf.ScdfProcessBookingActivityLogsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingRunServiceStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}, "rejectClientUserId": {"type": "string", "nullable": true}, "bookingRejectReasonId": {"type": "integer", "format": "int64"}, "bookingRejectReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.RejectBooking.Scdf.ScdfRejectBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingRequest": {"type": "object", "properties": {"account": {"type": "string", "nullable": true}, "bookingId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.RejectBooking.Spf.SpfRejectBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingRequest": {"required": ["journeys"], "type": "object", "properties": {"bookingPurposeId": {"type": "integer", "format": "int64"}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "requestor": {"type": "string", "nullable": true}, "isBookingForOtherParty": {"type": "boolean"}, "requestedForClientUserId": {"type": "string", "nullable": true}, "startTs": {"type": "string", "format": "date-time"}, "endTs": {"type": "string", "format": "date-time"}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64", "nullable": true}, "driverType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.DriverType"}, "requestClientDriverId": {"type": "string", "nullable": true}, "equipmentType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.EquipmentType"}, "accessories": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "remarks": {"type": "string", "nullable": true}, "pickupSiteLocationId": {"type": "integer", "format": "int32"}, "journeyType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyType"}, "journeys": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.JourneyLocation"}, "nullable": true}, "vehicleCommanderType": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.VehicleCommanderType"}, "vehicleCommanderClientUserId": {"type": "string", "nullable": true}, "numberOfPassengers": {"type": "integer", "format": "int32"}, "equipmentAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.InputEquipmentAttachment"}, "nullable": true}, "equipmentAttachmentIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UpdateBooking.Scdf.ScdfUpdateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingRequest": {"type": "object", "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UpdateBooking.Spf.SpfUpdateBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Booking"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingRequest": {"type": "object", "properties": {"imageBase64": {"type": "string", "nullable": true}, "extension": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Features.UploadSingleImageBooking.Scdf.ScdfUploadSingleImageBookingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.TempUploadImageStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.AdditionalLocations": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.Common.AdditionalLocation"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.Booking": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "statusId": {"type": "integer", "format": "int64", "nullable": true}, "status": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "purpose": {"type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleRegistration": {"type": "string", "nullable": true}, "pickupTime": {"type": "string", "format": "date-time", "nullable": true}, "dropoffTime": {"type": "string", "format": "date-time", "nullable": true}, "type": {"type": "string", "nullable": true}, "requestClientUserId": {"type": "string", "nullable": true}, "requestedBy": {"type": "string", "nullable": true}, "requestedDate": {"type": "string", "format": "date-time", "nullable": true}, "journeys": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.Journey"}, "nullable": true}, "pickupLocationId": {"type": "integer", "format": "int64", "nullable": true}, "pickupLocationName": {"type": "string", "nullable": true}, "requestClientDriverId": {"type": "string", "nullable": true}, "driverName": {"type": "string", "nullable": true}, "driverEmail": {"type": "string", "nullable": true}, "remarks": {"type": "string", "nullable": true}, "bookingPurposeId": {"type": "integer", "format": "int64", "nullable": true}, "bookingPurposeTitle": {"type": "string", "nullable": true}, "bookingPurposeDescription": {"type": "string", "nullable": true}, "vehicleCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleCategoryName": {"type": "string", "nullable": true}, "keyReturnTs": {"type": "string", "format": "date-time", "nullable": true}, "keyCollectionTs": {"type": "string", "format": "date-time", "nullable": true}, "accessories": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.BookingAccessory"}, "nullable": true}, "approvedManagers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.DisplayUser"}, "nullable": true}, "rejectedManagers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.IO.Http.DisplayUser"}, "nullable": true}, "commanderClientUserId": {"type": "string", "nullable": true}, "commanderUsername": {"type": "string", "nullable": true}, "requestedForClientUserId": {"type": "string", "nullable": true}, "locationType": {"type": "integer", "format": "int32", "nullable": true}, "driverType": {"type": "integer", "format": "int32", "nullable": true}, "vehicleCommanderType": {"type": "integer", "format": "int32"}, "equipmentType": {"type": "integer", "format": "int32", "nullable": true}, "numberOfPassengers": {"type": "integer", "format": "int32", "nullable": true}, "bookingType": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.BookingAccessory": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "typeId": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.BookingActivityLogs": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}, "bookingStatus": {"type": "string", "nullable": true}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.ActivityLogEntry"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.BookingAttachment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "bookingId": {"type": "integer", "format": "int64"}, "urlAttachment": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "base64Content": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.BookingRunServiceStatus": {"type": "object", "properties": {"status": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.BookingStatus": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int64"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.CancelBookingReasons": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.CancelBookingReason"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.DisplayUser": {"type": "object", "properties": {"clientUserId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.ForceTerminateBookingReasons": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.ForceTerminateBookingReason"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.Journey": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "location": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "order": {"type": "integer", "format": "int32", "nullable": true}, "journeyLocationType": {"type": "string", "nullable": true}, "journeyLocationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.RejectBookingReasons": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.RejectBookingReason"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.RequestPurposeVehicleCategoryMaps": {"type": "object", "properties": {"total": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.RequestPurposeVehicleCategoryMap"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.RequestPurposes": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Booking.Domain.RequestPurpose"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.TempDeleteImageStatus": {"type": "object", "properties": {"guid": {"type": "string", "nullable": true}, "extension": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.IO.Http.TempUploadImageStatus": {"type": "object", "properties": {"guid": {"type": "string", "nullable": true}, "extension": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Booking.Infratructure.BookingResponseStatus": {"type": "object", "properties": {"bookingId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "isSucceeded": {"type": "boolean", "nullable": true}, "errMsg": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.ChangeType": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Common.ClientUserChange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ChangeType"}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "clientUserId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "currentValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "previousValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "changedBy": {"type": "string", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.EntityType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Common.IssuanceSettingsPurposeChange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ChangeType"}, "bookingPurposeId": {"type": "integer", "format": "int64", "nullable": true}, "bookingPurposeName": {"type": "string", "nullable": true}, "currentValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "previousValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "changedBy": {"type": "string", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.IssuanceSettingsRulesChange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ChangeType"}, "bookingRuleId": {"type": "integer", "format": "int64", "nullable": true}, "bookingRuleName": {"type": "string", "nullable": true}, "currentValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "previousValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "changedBy": {"type": "string", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.IssuanceSettingsVehicleCategoriesChange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ChangeType"}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "bookingVehicleType": {"type": "string", "nullable": true}, "currentValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "previousValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "changedBy": {"type": "string", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.LoginChange": {"type": "object", "properties": {"clientId": {"type": "integer", "format": "int64", "nullable": true}, "clientUserId": {"type": "string", "nullable": true}, "clientUserName": {"type": "string", "nullable": true}, "clientLoginSourceIP": {"type": "string", "nullable": true}, "clientDetails": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "eventTs": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Common.LoginMode": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Common.LoginResult": {"enum": [1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Common.VehicleChange": {"type": "object", "properties": {"type": {"$ref": "#/components/schemas/Cartrack.Fleet.Common.ChangeType"}, "vehicleId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleRegistration": {"type": "string", "nullable": true}, "currentValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "previousValue": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "changedBy": {"type": "string", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Domain.Common.DriverDepartment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Domain.DriverPdpLicense": {"type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}, "licenseNumber": {"type": "string", "nullable": true}, "licenseIssueDate": {"type": "string", "format": "date-time", "nullable": true}, "licenseExpiryDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Domain.DriverQdlLicense": {"required": ["licenseTypeId"], "type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}, "licenseNumber": {"type": "string", "nullable": true}, "licenseValidStart": {"type": "string", "format": "date-time", "nullable": true}, "licenseValidEnd": {"type": "string", "format": "date-time", "nullable": true}, "licenseFirstIssueDate": {"type": "string", "format": "date-time", "nullable": true}, "licenseIssuedCountry": {"type": "string", "nullable": true}, "licensePoints": {"type": "integer", "format": "int32", "nullable": true}, "licenseDriverRestrictions": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Domain.Gender": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "Cartrack.Fleet.Driver.Features.GetDepartments.GetDepartmentsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverDepartments"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetDriver.GetDriverResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.Driver"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetDriverByClientUserId.GetDriverByClientUserIdResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.Driver"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetDrivers.GetDriversResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.Drivers"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetDriversByUserId.GetDriversByUserIdResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverSlim"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetPdpLicenses.GetPdpLicensesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverPdpLicenses"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.GetQdlLicenses.GetQdlLicensesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverQdlLicenses"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverRequest": {"type": "object", "properties": {"driverName": {"type": "string", "nullable": true}, "driverSurName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "eMail": {"type": "string", "nullable": true}, "idNumber": {"type": "string", "nullable": true}, "gender": {"type": "integer", "format": "int32", "nullable": true}, "enabled": {"type": "integer", "format": "int32", "nullable": true}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "driverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverQdlLicense"}, "nullable": true}, "specialDriverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverPdpLicense"}, "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.Features.UpdateDriver.UpdateDriverResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.Driver"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.Driver": {"required": ["clientDriverId"], "type": "object", "properties": {"clientDriverId": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "surname": {"type": "string", "nullable": true}, "idNumber": {"type": "string", "nullable": true}, "gender": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.Gender"}, "phoneNumber": {"type": "string", "nullable": true}, "createdTs": {"type": "string", "format": "date-time", "nullable": true}, "updatedTs": {"type": "string", "format": "date-time", "nullable": true}, "clientUserId": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "enabled": {"type": "string", "nullable": true}, "employeeNumber": {"type": "string", "nullable": true}, "departments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.Common.DriverDepartment"}, "nullable": true}, "driverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverQdlLicense"}, "nullable": true}, "specialDriverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverPdpLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverDepartments": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.Common.DriverDepartment"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverPdpLicenseSlim": {"type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverPdpLicenses": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverPdpLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverQdlLicenseSlim": {"required": ["licenseTypeId"], "type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverQdlLicenses": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.Domain.DriverQdlLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.DriverSlim": {"type": "object", "properties": {"clientDriverId": {"type": "string", "nullable": true}, "idNumber": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "normalLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverQdlLicenseSlim"}, "nullable": true}, "specialLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverPdpLicenseSlim"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Driver.IO.Http.Drivers": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.Driver"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.DisqualificationTfms": {"type": "object", "properties": {"disqualificationStartDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "disqualificationEndDateUtc": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.ElitesPdpTfms": {"type": "object", "properties": {"idNo": {"type": "string", "nullable": true}, "pdpDetails": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.PdpDetailsTfms"}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.ElitesQdlTfms": {"type": "object", "properties": {"idNo": {"type": "string", "nullable": true}, "totalDemeritPts": {"type": "integer", "format": "int32", "nullable": true}, "qdlDetails": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.QdlDetailsTfms"}, "pdlDetails": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.PdlDetailsTfms"}, "suspension": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.SuspensionTfms"}, "disqualification": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.DisqualificationTfms"}, "revocation": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.RevocationTfms"}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.PdlDetailsTfms": {"type": "object", "properties": {"pdlValidity": {"type": "string", "nullable": true}, "pdlExpiryDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "pdlClasses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.QdlClassItemTfms"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.PdpClassItemTfms": {"type": "object", "properties": {"licenseClass": {"type": "string", "nullable": true}, "issueDateUtc": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.PdpDetailsTfms": {"type": "object", "properties": {"permitNo": {"type": "string", "nullable": true}, "validity": {"type": "string", "nullable": true}, "pdpStatus": {"type": "string", "nullable": true}, "joinDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "issueDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "pdpClasses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.PdpClassItemTfms"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.QdlClassItemTfms": {"type": "object", "properties": {"licenseClass": {"type": "string", "nullable": true}, "issueDateUtc": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.QdlDetailsTfms": {"type": "object", "properties": {"qdlValidity": {"type": "string", "nullable": true}, "qdlExpiryDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "qdlClasses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.QdlClassItemTfms"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.RevocationTfms": {"type": "object", "properties": {"revocationStartDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "revocationEndDateUtc": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Domain.SuspensionTfms": {"type": "object", "properties": {"suspensionStartDateUtc": {"type": "string", "format": "date-time", "nullable": true}, "suspensionEndDateUtc": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.GetLogs.GetLogsResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Logs.Domain.Log"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.GetPdpLicense.GetPdpLicenseResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.ElitesPdpTfms"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.GetQdlLicense.GetQdlLicenseResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Domain.ElitesQdlTfms"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingRequest": {"type": "object", "properties": {"clientDriverId": {"type": "string", "nullable": true}, "vehicleId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.IsLicenseMatching.IsLicenseMatchingResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.IsLicenseMatching.LicenseCheck"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.IsLicenseMatching.LicenseCheck": {"type": "object", "properties": {"isMatch": {"type": "boolean"}, "validationError": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.ValidateLicenses.ValidateCheck": {"type": "object", "properties": {"isMatch": {"type": "boolean"}, "validationError": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesRequest": {"type": "object", "properties": {"vehicleRegistration": {"type": "string", "nullable": true}, "driverId": {"type": "string", "nullable": true}, "driverNormalLicenses": {"type": "array", "items": {"type": "string"}, "nullable": true}, "driverSpecialLicenses": {"type": "array", "items": {"type": "string"}, "nullable": true}, "vehicleNormalLicenses": {"type": "array", "items": {"type": "string"}, "nullable": true}, "vehicleSpecialLicenses": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.License.Features.ValidateLicenses.ValidateLicensesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.License.Features.ValidateLicenses.ValidateCheck"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Logs.Domain.Log": {"type": "object", "properties": {"source": {"type": "string", "nullable": true}, "level": {"type": "string", "nullable": true}, "group": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "createdTs": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Common.AppSettings": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "isMainUserSettingEnabled": {"type": "boolean", "nullable": true}, "isSubUserSettingEnabled": {"type": "boolean", "nullable": true}, "userNameLimit": {"type": "string", "nullable": true}, "isSubUserDefaultValueIfAllowed": {"type": "string", "nullable": true}, "isMainUserRequiredValueToAllowSubUserOverride": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Common.UserAppSettingData": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "isMainUserSettingEnabled": {"type": "boolean", "nullable": true}, "isSubUserSettingEnabled": {"type": "boolean", "nullable": true}, "userNameLimit": {"type": "string", "nullable": true}, "isSubUserDefaultValueIfAllowed": {"type": "string", "nullable": true}, "isMainUserRequiredValueToAllowSubUserOverride": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Common.UserAppSettings": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "clientUserId": {"type": "string", "nullable": true}, "pricingPlanId": {"type": "integer", "format": "int64"}, "globalAppSettings": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.AppSettings"}, "nullable": true}, "pricingPlanAppSettings": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettingData"}, "nullable": true}, "parentAppSettingsValue": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettingData"}, "nullable": true}, "clientAppSettings": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettingData"}, "nullable": true}, "subUserAppSettings": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettingData"}, "nullable": true}, "appSettings": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettingData"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Common.UserDepartment": {"type": "object", "properties": {"clientUserId": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.DriverPermissions": {"type": "object", "properties": {"canViewDriverList": {"type": "boolean"}, "canAddDriver": {"type": "boolean"}, "canAddGroup": {"type": "boolean"}, "canDeactivateDriver": {"type": "boolean"}, "canEditDriver": {"type": "boolean"}, "canImportDrivers": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.IssuancePermissions": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "canAddRequestPurpose": {"type": "boolean"}, "canAddVehicleCategory": {"type": "boolean"}, "canCreateBackdatedBooking": {"type": "boolean"}, "canAllowVehicleSelectionDuringBooking": {"type": "boolean"}, "canAllowVehicleTypeDuringBooking": {"type": "boolean"}, "canApproveBookingRequest": {"type": "boolean"}, "canAutoApproveBookingRequest": {"type": "boolean"}, "canViewCalendar": {"type": "boolean"}, "canCancelBookingRequest": {"type": "boolean"}, "canChangeBookingToActive": {"type": "boolean"}, "canDeclineBookingRequest": {"type": "boolean"}, "canEndBookingRequest": {"type": "boolean"}, "canViewOnlyMyBookings": {"type": "boolean"}, "canDeleteRequestPurpose": {"type": "boolean"}, "canDeleteVehicleCategory": {"type": "boolean"}, "canEditAvailableVehicles": {"type": "boolean"}, "canEditBookingRequest": {"type": "boolean"}, "canEditRequestPurpose": {"type": "boolean"}, "canEditRules": {"type": "boolean"}, "canEditVehicleCategory": {"type": "boolean"}, "canForceTerminateBooking": {"type": "boolean"}, "canViewResources": {"type": "boolean"}, "canViewSettings": {"type": "boolean"}, "canViewTermsAndConditions": {"type": "boolean"}, "canViewAvailableVehicles": {"type": "boolean"}, "canViewRules": {"type": "boolean"}, "canViewVehicleCategory": {"type": "boolean"}, "canViewVehicle": {"type": "boolean"}, "canViewRequestPurpose": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.ListPermissions": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}, "vehiclePermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.VehiclePermissions"}, "driverPermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.DriverPermissions"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.MapPermissions": {"type": "object", "properties": {"canViewMap": {"type": "boolean"}, "canCompareVehicles": {"type": "boolean"}, "canEnableDateSelection": {"type": "boolean"}, "canEnableTripDownload": {"type": "boolean"}, "canFollowVehicle": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.ReportPermissions": {"type": "object", "properties": {"canViewReports": {"type": "boolean"}, "canCustomise": {"type": "boolean"}, "canViewFavorites": {"type": "boolean"}, "canViewInformation": {"type": "boolean"}, "canViewSetup": {"type": "boolean"}, "canSetupDownloadReport": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.SetOfPermissions": {"type": "object", "properties": {"canManageUsers": {"type": "boolean"}, "canManageRoles": {"type": "boolean"}, "issuancePermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.IssuancePermissions"}, "listPermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.ListPermissions"}, "mapPermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.MapPermissions"}, "reportPermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.ReportPermissions"}, "userPermissions": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.UserPermissions"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.UserPermissions": {"type": "object", "properties": {"canManageUsers": {"type": "boolean"}, "canAudit": {"type": "boolean"}, "canViewProfileSettings": {"type": "boolean"}, "canEditProfileSettings": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.Permissions.VehiclePermissions": {"type": "object", "properties": {"canViewVehicles": {"type": "boolean"}, "canAddGroup": {"type": "boolean"}, "canAllowUpdatingOfVehicleOdometer": {"type": "boolean"}, "canDeactivateVehicle": {"type": "boolean"}, "canEditVehicle": {"type": "boolean"}, "canImportVehiclesToGroups": {"type": "boolean"}, "canViewStatus": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.DriverAccess": {"type": "object", "properties": {"driverName": {"type": "string", "nullable": true}, "driverId": {"type": "string", "nullable": true}, "canViewDriver": {"type": "boolean"}, "canEditDriver": {"type": "boolean"}, "canDeactivateDriver": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.DriverGroupAccess": {"type": "object", "properties": {"canViewGroup": {"type": "boolean"}, "canEditGroup": {"type": "boolean"}, "canRemoveGroup": {"type": "boolean"}, "canViewDrivers": {"type": "boolean"}, "driverGroupName": {"type": "string", "nullable": true}, "driverGroupId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.DriversAndGroupsAccess": {"type": "object", "properties": {"driverGroups": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.DriverGroupAccess"}, "nullable": true, "readOnly": true}, "drivers": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.DriverAccess"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.VehicleAccess": {"type": "object", "properties": {"vehicleRegistration": {"type": "string", "nullable": true}, "canViewVehicle": {"type": "boolean"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.VehicleGroupAccess": {"type": "object", "properties": {"canViewGroup": {"type": "boolean"}, "canEditGroup": {"type": "boolean"}, "canRemoveGroup": {"type": "boolean"}, "canViewVehicles": {"type": "boolean"}, "vehicleGroupName": {"type": "string", "nullable": true}, "vehicleGroupId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Domain.UserDataAccess.VehiclesAndGroupsAccess": {"type": "object", "properties": {"vehicleGroups": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.VehicleGroupAccess"}, "nullable": true, "readOnly": true}, "vehicles": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.VehicleAccess"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "userName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "cellNumber": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}, "serviceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.CreateUser.Spf.CreateUserResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.Infratructure.ResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.DeleteUser.DeleteUserResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.Infratructure.ResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetDepartmentsById.GetUserDepartmentResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUserDepartment"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetDriverAndGroupsAccess.GetDriverAndGroupsAccessResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.DriversAndGroupsAccess"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetPermissions.GetPermissionsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Permissions.SetOfPermissions"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetUserAppSettingsById.GetUserAppSettingsByIdResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.Common.UserAppSettings"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetUserById.GetUserByResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetUserByUsername.GetUserByNameResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetUsers.GetUserResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetUsersByRole.GetUserByRoleResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.EFCore.Models.Fleet.ClientUser"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.GetVehicleAndGroupsAccess.GetVehicleAndGroupsAccessResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Domain.UserDataAccess.VehiclesAndGroupsAccess"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.Infratructure.ResponseStatus": {"type": "object", "properties": {"clientUserId": {"type": "string", "nullable": true}, "isSucceeded": {"type": "boolean", "nullable": true}, "errMsg": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "clientUserId": {"type": "string", "nullable": true}, "lastLoginDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.UpdateLastLoginDate.UpdateLastLoginResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.Infratructure.ResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.UpdateUser.UpdateUserRequest": {"type": "object", "properties": {"clientUserId": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "cellNumber": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.User.Features.UpdateUser.UpdateUserResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.User.Features.Infratructure.ResponseStatus"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Domain.Common.VehicleDepartment": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Domain.VehiclePdpLicense": {"type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Domain.VehicleQdlLicense": {"type": "object", "properties": {"licenseTypeId": {"type": "integer", "format": "int64"}, "licenseName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetDriversForVehicle.Scdf.GetAllowedDriversForVehicleResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Driver.IO.Http.DriverSlim"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleById.GetVehicleByIdResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicle"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleByRegistration.GetVehicleByRegistrationResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicle"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleCategories.GetVehicleCategoriesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleCategories"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleDepartments.GetVehicleDepartmentsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleDepartments"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleForDriver.Scdf.GetVehicleForDriverResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicle"}, "nullable": true}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleGroups.GetVehicleGroupsResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleGroups"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehiclePdpLicenses.GetVehiclePdpLicensesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehiclePdpLicenses"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicleQdlLicenses.GetVehicleQdlLicensesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleQdlLicenses"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehicles.GetVehiclesResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicles"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleCategory.GetVehiclesByVehicleCategoryResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicles"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.GetVehiclesByVehicleGroup.GetVehiclesByVehicleGroupResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicles"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleRequest": {"type": "object", "properties": {"vehicleName": {"type": "string", "nullable": true}, "clientVehicleDescription": {"type": "string", "nullable": true}, "fuelTargetConsumption": {"type": "number", "format": "double", "nullable": true}, "vehicleEngineTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleCategoryId": {"type": "integer", "format": "int64", "nullable": true}, "defaultDriver": {"type": "string", "nullable": true}, "defaultSiteLocationId": {"type": "integer", "format": "int64", "nullable": true}, "bookingAllocationPriority": {"type": "number", "format": "double", "nullable": true}, "vehicleStatusOptionId": {"type": "integer", "format": "int64", "nullable": true}, "monthlyMileageLimit": {"type": "integer", "format": "int32", "nullable": true}, "departmentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "driverLicenseIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "specialDriverLicenseIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "isPoolActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.Features.UpdateVehicle.UpdateVehicleResponse": {"type": "object", "properties": {"value": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicle"}, "Success": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.Vehicle": {"required": ["registration", "vehicleId"], "type": "object", "properties": {"vehicleId": {"type": "integer", "format": "int64"}, "registration": {"type": "string", "nullable": true}, "vehicleName": {"type": "string", "nullable": true}, "clientVehicleDescription": {"type": "string", "nullable": true}, "fuelTargetConsumption": {"type": "number", "format": "double", "nullable": true}, "vehicleEngineTypeId": {"type": "integer", "format": "int64", "nullable": true}, "maintenanceId": {"type": "integer", "format": "int32", "nullable": true}, "defaultSiteLocationId": {"type": "integer", "format": "int64", "nullable": true}, "isPoolActive": {"type": "boolean", "nullable": true}, "maintenanceStatusId": {"type": "integer", "format": "int64", "nullable": true}, "bookingVehicleTypeId": {"type": "integer", "format": "int64", "nullable": true}, "vehicleStatusOptionId": {"type": "integer", "format": "int64", "nullable": true}, "bookingAllocationPriority": {"type": "number", "format": "double", "nullable": true}, "speedSourceId": {"type": "integer", "format": "int32", "nullable": true}, "defaultDriver": {"type": "string", "nullable": true}, "createdTs": {"type": "string", "format": "date-time", "nullable": true}, "updatedTs": {"type": "string", "format": "date-time", "nullable": true}, "departments": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.Common.VehicleDepartment"}, "nullable": true}, "driverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.VehicleQdlLicense"}, "nullable": true}, "specialDriverLicenses": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.VehiclePdpLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleCategories": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleCategory"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleCategory": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "vehicleCount": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleDepartments": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.Common.VehicleDepartment"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleGroup": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "vehicleCount": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleGroups": {"type": "object", "properties": {"totalCount": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.VehicleGroup"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehiclePdpLicenses": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.VehiclePdpLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.VehicleQdlLicenses": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.Domain.VehicleQdlLicense"}, "nullable": true}}, "additionalProperties": false}, "Cartrack.Fleet.Vehicle.IO.Http.Vehicles": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Cartrack.Fleet.Vehicle.IO.Http.Vehicle"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Microsoft.AspNetCore.Identity.PasswordVerificationResult": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}}}}