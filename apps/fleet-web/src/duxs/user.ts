import { match } from 'ts-pattern'
import { createAction, createSelector } from '@reduxjs/toolkit'
import moment, { type MomentInput } from 'moment-timezone'
import type { Opaque, ReadonlyDeep, Simplify } from 'type-fest'
import type { History } from 'history'
import type { UseStore as IdBStore } from 'idb-keyval'

import { isTrue } from 'src/util-functions/validation'
import { daysTo } from 'src/util-functions/functional-utils'
import {
  isNilOrEmptyString,
  isNilOrEmptyTrimmedStringOrFalseish,
  isNonEmptyTrimmedString,
  isNonEmptyTrimmedStringAndNotFalseish,
} from 'src/util-functions/string-utils'
import { FETCH_ENGINE_SUCCESS } from 'duxs/engine'
import { matchEmailOrSmsOtp } from 'src/modules/app/authentication/slice'

import type {
  FailedLoginResponse,
  SuccessfulLoginResponse,
  SuccessfulPreLoginResponse,
} from 'src/api/user'
import type userAPI from 'src/api/user'
import type { Ct_fleet_contact_us, EmailType, Login } from 'api/user/types'
import type {
  FixMeAny,
  ISO3166_1Alpha2CountryCode,
  PromiseResolvedType,
} from 'src/types'
import type { UserSchema } from 'src/modules/admin/user-settings/shared/schemas'
import type { AppState } from 'src/root-reducer'
import type { ParsedKarooUiCustomizableTheme } from 'api/user/utils'
import type { VehicleDisplayName } from 'src/modules/dashboard/api/types'
import type { LoginSchema } from 'src/modules/app/authentication/login'
import type { GoogleMapsLocaleCode } from 'src/types/extended/google-maps'
import { createSelectorWithStrictMode } from 'src/redux-utils'

import {
  getSettings_UNSAFE,
  getTimeZones,
  getPreferences,
  getSettings,
} from './user-sensitive-selectors'
import {
  MAP_MAX_ZOOM,
  MAP_MIN_ZOOM,
  MAP_CLUSTER_MAX_ZOOM,
} from 'src/util-functions/constants'
import { parseBooleanUserSetting, parseImagePathUserSetting } from './utils'
import { DateTime } from 'luxon'
import { ctIntl } from 'src/util-components/ctIntl'
import { defaultStyles } from 'src/util-functions/client-default-styles'
import { z } from 'zod'
import type { ExcludeStrict, ExtractStrict } from 'src/types/utils'
import { clientUserIdSchema, type TerminalSerial, type VehicleId } from 'api/types'
import { batchReduxStateUpdatesFromSaga } from 'src/sagas/actions'
import type { HubConnection, ISubscription } from '@microsoft/signalr'
import { isBoolean, isEmpty, isNil } from 'lodash'
export { getSettings_UNSAFE, getSettings }

const MAP_VEHICLES_POSITIONS_DEFAULT_REFRESH_RATE_IN_SECONDS = 30

export type AuthenticatedUserUniqueId = Opaque<string, 'AuthenticatedUserUniqueId'>

type LoginMethodType = 'otp' | 'sso' | 'credentials_login' | 'federated'

// Actions
export const sisenseLoginSuccess = createAction<{
  sisenseApi: string
}>('sisenseLoginSuccess')

export const loginFailed = createAction<
  FailedLoginResponse | { unexpectedError: string }
>('login/failed')

export const handleSaveProfileSuccess = createAction('handleSaveProfileSuccess')
export const onUpdateVehicleLivePositionSuccess = createAction<
  PromiseResolvedType<typeof userAPI.updateVehicleLivePosition>
>('onUpdateVehicleLivePositionSuccess')

export const loginSucceeded = createAction<{
  loginMethodType: LoginMethodType
  apiData: SuccessfulLoginResponse
  preferences: FixMeAny
  debug: boolean
  vehicleIdToSelectOnMap: VehicleId | undefined
  refreshTokenTimeoutId: number | null
  isSCDF: boolean
}>('login/succeeded')

export const tokenLoginSucceeded =
  createAction<PromiseResolvedType<typeof userAPI.tokenLogin>>('tokenLogin/succeeded')

type LoginPayloadData = (
  | ReturnType<typeof login>
  | ReturnType<typeof federatedLogin>
)['payload']

export const receivedThirdPartyPreLoginData = createAction<{
  vehicleList: Array<VehicleListType>
  loginPayloadData: LoginPayloadData
}>('login/receivedThirdPartyPreLoginData')

export const receivedTwoFAPreLoginData = createAction<{
  email: EmailType
  phone: string | null
  loginData:
    | ({ type: 'credentials_login' } & LoginSchema)
    | { type: 'federated'; federatedResponse: string }
}>('login/receivedTwoFAPreLoginData')

export const submittedThirdPartyLogin = createAction<{ vehicle: string }>(
  'login/submittedThirdPartyLogin',
)

export const setUserLocale = createAction<{
  locale: ExcludeStrict<State['locale'], undefined>
}>('login/setUserLocale')

export const setDashboardVehicleDisplayName = createAction<VehicleDisplayName>(
  'dashboard/setDashboardVehicleDisplayName',
)

export const getDashboardCustomName = (state: AppState): string => {
  const dashboardCustomName = state.user.settings.dashboardCustomName

  if (typeof dashboardCustomName === 'string') {
    return dashboardCustomName
  }
  return DEFAULT_DASHBOARD_CUSTOM_NAME
}

export const getDashboardCoachingName = (state: AppState): string | null => {
  const dashboardCoachingName = state.user.settings.dashboardCoachingName

  if (typeof dashboardCoachingName === 'string') {
    return dashboardCoachingName
  }
  return null
}

export const logoutFailed = createAction('logout/failed')

export const resetUserStateBeforeAttemptingALogin = createAction<
  ReturnType<typeof login>['payload']
>('resetUserStateBeforeAttemptingALogin')

export const loggedOutFromAnotherTab = createAction('loggedOutFromAnotherTab')

export const startedPreLoginFetch = createAction('preLogin/started')
export const failedPreLoginFetch = createAction('preLogin/failed')
export const fetchPreLoginData = createAction<{ history: History }>('preLogin/fetch')

export const receivePreLoginData = createAction<{
  languageList: SuccessfulPreLoginResponse['languages']
  styleProperties: Record<string, unknown>
  locale: SuccessfulPreLoginResponse['languages']['options'][number]['value']
  ctCountries: SuccessfulPreLoginResponse['ctCountries']
  federatedLogins: SuccessfulPreLoginResponse['federatedLogins']
  countriesWebsites: SuccessfulPreLoginResponse['countriesWebsites']
}>('receivePreLoginData')

export const initKeyIdbState = createAction<{ key: string; value: unknown }>(
  'initKeyIdbState',
)

export const setKeyIdbStateWithReduxSync = createAction<{
  key: string
  setStateAction: (value: unknown) => unknown
  store: IdBStore
}>('setKeyIdbStateWithReduxSync')

export const setKeyIdbStateOnRedux = createAction<{
  key: string
  value: unknown
}>('setKeyIdbStateOnRedux')

export const deleteKeyIdbSyncState = createAction<{ key: string }>(
  'deleteKeyIdbSyncState',
)

export const startLogOut = createAction('startLogOut')
export const DEFAULT_DASHBOARD_CUSTOM_NAME = 'CUSTOM'
export const LOG_IN = 'LOG_IN'
export const FEDERATED_LOG_IN = 'FEDERATED_LOG_IN'
export const TOKEN_LOG_IN = 'TOKEN_LOG_IN'
export const SUB_USER_LOGIN = 'SUB_USER_LOGIN'
export const SET_USER_PASSWORD = 'SET_USER_PASSWORD'
export const PASSWORD_UPDATE_STATUS = 'PASSWORD_UPDATE_STATUS'
export const REGISTER = 'REGISTER'
export const LOGGED_OUT = 'LOGGED_OUT'
export const SAVE_PROFILE = 'SAVE_PROFILE'
export const ON_SAVE_PROFILE = 'ON_SAVE_PROFILE'
export const SAVE_PREFERENCE = 'SAVE_PREFERENCE'
export const SET_SYSTEM_STATE_MESSAGE = 'SET_SYSTEM_STATE_MESSAGE'
export const UPDATE_USER_IMAGE = 'UPDATE_USER_IMAGE'
export const SUBMIT_HELP_REQUEST = 'SUBMIT_HELP_REQUEST'
export const RECEIVE_USER_SETTINGS = 'RECEIVE_USER_SETTINGS'
export const TOGGLE_SSO_HASH = 'TOGGLE_SSO_HASH'
export const TOGGLE_API_KEY = 'TOGGLE_API_KEY'
export const SUBMIT_CLEAR_FUEL = 'SUBMIT_CLEAR_FUEL'
export const UPDATE_STYLE_SETTINGS = 'UPDATE_STYLE_SETTINGS'
export const SET_DASHBOARD_INDUSTRY = 'SET_DASHBOARD_INDUSTRY'

export type ViewMode = 'detailed' | 'compact'

// Reducer
export const defaultPreferences = {
  vehicleDisplayName: 'name',
  mapVehicleLabels: {
    name: true,
    registration: false,
    description: false,
    description1: false,
    description2: false,
    odometer: false,
    driver: false,
    unitRawClock: false,
    location: false,
  },
  leftPanelVehicleSubTitles: {
    ratingLabel: true,
    driverLabel: false,
    vehicleGroup: false,
  },
  geofenceColors: [],
  poiColors: [],
  vehicleIconColors: [],
  useVehicleIconColor: false,
  showEventInfoPanel: false,
  showTaskInfoPanel: false,
  mapViewSortMethods: {
    default: 'alphabetical',
    vehicles: 'alphabetical',
    drivers: 'alphabetical',
  },
  mapViewGroups: {
    vehicles: false,
    drivers: false,
  },
  tripsTablesPreferences: {
    viewModeOptions: ['detailed', 'compact'],
    selectedViewMode: 'detailed',
    visibility: {
      zeroKmTrips: true,
      stopTime: false,
      flagged: false,
    },
  },
} as const satisfies UserPreferences

export const scdfDefaultPreferences = {
  vehicleDisplayName: 'name',
  mapVehicleLabels: {
    name: true,
    registration: false,
    description: false,
    description1: false,
    description2: false,
    odometer: false,
    driver: true,
    unitRawClock: false,
    location: false,
  },
  leftPanelVehicleSubTitles: {
    ratingLabel: false,
    driverLabel: true,
    vehicleGroup: true,
  },
  geofenceColors: [],
  poiColors: [],
  vehicleIconColors: [],
  useVehicleIconColor: false,
  showEventInfoPanel: false,
  showTaskInfoPanel: false,
  mapViewSortMethods: {
    default: 'alphabetical',
    vehicles: 'alphabetical',
    drivers: 'alphabetical',
  },
  mapViewGroups: {
    fleet: true,
    vehicles: false,
    drivers: false,
  },
  tripsTablesPreferences: {
    viewModeOptions: ['detailed', 'compact'],
    selectedViewMode: 'detailed',
    visibility: {
      zeroKmTrips: true,
      stopTime: false,
      flagged: false,
    },
  },
} as const satisfies UserPreferences

export type FederatedLogins = SuccessfulPreLoginResponse['federatedLogins'] | undefined

export type VehicleListType = {
  label: string
  name: string
  value: string
}

export type UserPreferences = ReadonlyDeep<{
  reportSchedule?: Record<string, FixMeAny>
  miFleetDashboard?: Array<FixMeAny>
  vehicleEventsTableId?: Record<string, FixMeAny>
  vehicleEventsTableIdBasic?: Record<string, FixMeAny>
  pointToPointTableId?: Record<string, FixMeAny>
  timelineSpeed?: number
  miFleetDashboardCostTotalsFilters?: FixMeAny
  vehicleDisplayName:
    | 'name'
    | 'registration'
    | 'description'
    | 'description1'
    | 'description2'
  mapViewSortMethods: Record<string, 'alphabetical' | 'status'>
  mapViewGroups: Record<string, any>
  mapVehicleLabels: {
    name: boolean
    registration: boolean
    description: boolean
    description1: boolean
    description2: boolean
    odometer: boolean
    driver: boolean
    unitRawClock: boolean
    location: boolean
  }
  leftPanelVehicleSubTitles: {
    ratingLabel: boolean
    driverLabel: boolean
    vehicleGroup: boolean
  }
  geofenceColors: Array<string>
  poiColors: Array<string>
  vehicleIconColors: Array<string>
  useVehicleIconColor: boolean
  showEventInfoPanel: boolean
  showTaskInfoPanel: boolean
  tripsTablesPreferences: {
    viewModeOptions: Array<ViewMode>
    selectedViewMode: UserPreferences['tripsTablesPreferences']['viewModeOptions'][number]
    visibility: {
      zeroKmTrips: boolean
      stopTime: boolean
      flagged: boolean
    }
  }
}>

export type MapVehicleLabels = UserPreferences['mapVehicleLabels']
export type LeftPanelVehicleSubTitles = UserPreferences['leftPanelVehicleSubTitles']

export type DevicePositionStreamSubscriptionMsg = {
  eventTsUtc: string
  // NOT an extensive list of event types
  eventType: 'NotSupported' | 'Dhdg' | 'Per' | 'Corner' | 'DOdo' | 'DAdc1'
  receivedTsUtc: string
  serial: TerminalSerial
  position: {
    gpsFix: number
    gpsHeading: number
    latitude: number
    longitude: number
    odometerKm: number
    speed: number
  }
}
export type DevicePositionStreamSubscription =
  ISubscription<DevicePositionStreamSubscriptionMsg>

const baseUserSchema = z.object({
  id: z.string().min(1),
  cuid: clientUserIdSchema.nullish().catch(null),
  companyName: z.string().catch(''),
  username: z.string().catch(''),
})
export type LoggedInBaseUser = z.infer<typeof baseUserSchema>

const accountUserSchema = baseUserSchema.merge(
  z.object({
    isTokenAccount: z.literal(undefined).catch(undefined),
    account: z.string().catch(''),
    primaryEmail: z.string().catch(''),
  }),
)
export type LoggedInAccountUser = Simplify<z.infer<typeof accountUserSchema>>

const tokenUserSchema = baseUserSchema.merge(
  z.object({
    isTokenAccount: z.literal(true),
    authToken: z.string().catch(''),
  }),
)

const userSchema = accountUserSchema.or(tokenUserSchema)

type User = Simplify<z.infer<typeof userSchema>>

type State = ReadonlyDeep<{
  idbStates: { [idbKey: string]: unknown }
  countriesWebsites: SuccessfulPreLoginResponse['countriesWebsites']
  hubConnectionMeta: {
    connection: HubConnection
    devicePositionStreamSubscription: DevicePositionStreamSubscription | null
  } | null
  user: User | null
  settings: Record<string, unknown>
  timeZones: SuccessfulLoginResponse['timeZones'] | null
  diagnosticStatus: Record<string, FixMeAny>
  diagnostic: Record<string, any> | undefined
  diagnosticFaulty: Record<string, any>
  diagnosticHealthy: Record<string, any>
  preferences: Record<string, any> & UserPreferences
  rememberedUsername: string
  debug: boolean | undefined
  allowEditSensor: boolean
  disabled: string | undefined
  disable_reason: string | null | undefined
  passwordAge: string
  passwordUpdateStatus: {
    updatedTS: Date
    isSuccess: boolean
    response: string
  }
  /**
   * Similar to react-query statuses
   */
  preLoginQuery: {
    fetchStatus: 'fetching' | 'idle'
    status: 'pending' | 'error' | 'success'
  }
  loginApiData:
    | { status: 'IDLE' | 'PROCESSING' }
    | {
        status: 'SUCCEEDED'
        loginMethodType: LoginMethodType | 'token'
        login2FAStatus: ExtractStrict<
          SuccessfulLoginResponse,
          { status: 'SUCCEEDED' }
        >['status2FA']
      }
    | FailedLoginResponse
    | {
        status: FailedLoginResponse['status']
        error: 'UNEXPECTED_ERROR'
        message: string
      }
    | {
        status: Login.ThirdPartyResponse['status']
        vehicleList: Array<VehicleListType>
        loginPayloadData: LoginPayloadData
      }
    | {
        status: Login.TwoFALogin['status']
        email: EmailType
        phone: string | null
        loginData:
          | ({ type: 'credentials_login' } & LoginSchema)
          | { type: 'federated'; federatedResponse: string }
      }
  logoutMutation: {
    status: 'error' | 'idle' | 'pending' | 'success'
  }
  systemStateMessage: string | undefined
  locale:
    | SuccessfulPreLoginResponse['languages']['options'][number]['value']
    | undefined
  hardwareTypeList: SuccessfulLoginResponse['hardwareTypeList'] | undefined
  languageList: SuccessfulPreLoginResponse['languages'] | undefined
  ctCountriesList: SuccessfulPreLoginResponse['ctCountries']
  federatedLogins: FederatedLogins
  vehicleIdToSelectOnMapAfterLogin: VehicleId | undefined
  refreshTokenTimeoutId: number | null
  refreshJwtMutation: {
    status: 'error' | 'idle' | 'pending' | 'success'
  }
  jwtAccessToken: string | null
}>
export type UserState = State

const rawUserFromLocalStorage = (JSON.parse as FixMeAny)(
  localStorage.getItem('user'),
) as unknown

const parsedUserFromLocalStorage =
  userSchema.safeParse(rawUserFromLocalStorage).data ?? null

const initialState: State = {
  idbStates: {},
  countriesWebsites: false,
  user: parsedUserFromLocalStorage,
  settings: { styleProperties: {} },
  timeZones: (JSON.parse as FixMeAny)(
    sessionStorage.getItem('userTimezones') || localStorage.getItem('userTimezones'),
  ),
  diagnosticStatus:
    (JSON.parse as FixMeAny)(
      sessionStorage.getItem('diagnosticStatus') ||
        localStorage.getItem('diagnosticStatus'),
    ) ?? {},
  diagnostic: {},
  diagnosticFaulty: {},
  diagnosticHealthy: {},
  preferences: {
    ...defaultPreferences,
    ...(JSON.parse as FixMeAny)(
      sessionStorage.getItem('userPreferences') ||
        (parsedUserFromLocalStorage
          ? localStorage.getItem('userPreferences' + parsedUserFromLocalStorage.id)
          : null),
    ),
  },
  rememberedUsername: '',
  debug: false,
  allowEditSensor: window.location.search.includes('sensors=true'),
  disabled: undefined,
  disable_reason: '',
  passwordAge: '',
  passwordUpdateStatus: {
    updatedTS: new Date(),
    isSuccess: true,
    response: '',
  },
  preLoginQuery: {
    fetchStatus: 'idle',
    status: 'pending',
  },
  locale: undefined,
  hardwareTypeList: undefined,
  loginApiData: { status: 'IDLE' },
  logoutMutation: { status: 'idle' },
  systemStateMessage: undefined,
  languageList: undefined,
  ctCountriesList: [],
  federatedLogins: undefined,
  vehicleIdToSelectOnMapAfterLogin: undefined,
  refreshTokenTimeoutId: null,
  refreshJwtMutation: { status: 'idle' },
  jwtAccessToken: null,
  hubConnectionMeta: null,
}

const emptyState = {
  user: null,
  timeZones: null,
  preferences: defaultPreferences,
  rememberedUsername: '',
  systemStateMessage: '',
  hubConnectionMeta: null,
} satisfies Partial<State>

export default function reducer(state = initialState, action: FixMeAny): State {
  if (matchEmailOrSmsOtp.succeeded.match(action)) {
    const { payload } = action
    return {
      ...state,
      rememberedUsername:
        payload === 'NO_MATCHES' ? state.rememberedUsername : payload.user_name,
    }
  }

  if (receivedTwoFAPreLoginData.match(action)) {
    const { payload } = action
    return {
      ...state,
      loginApiData: {
        status: 'TWO_FACTOR_AUTHENTICATION',
        email: payload.email,
        phone: payload.phone,
        loginData: payload.loginData,
      },
    }
  }
  if (receivedThirdPartyPreLoginData.match(action)) {
    const { vehicleList, loginPayloadData } = action.payload
    return {
      ...state,
      loginApiData: {
        status: 'THIRD_PARTY_USER',
        vehicleList,
        loginPayloadData,
      },
    }
  }
  if (loginFailed.match(action)) {
    const { payload } = action
    return {
      ...state,
      loginApiData:
        'unexpectedError' in payload
          ? {
              status: 'FAILED',
              error: 'UNEXPECTED_ERROR',
              message: payload.unexpectedError,
            }
          : payload,
    }
  }
  if (loginSucceeded.match(action)) {
    const { payload } = action

    const { apiData, isSCDF } = payload

    return {
      ...state,
      loginApiData: {
        status: 'SUCCEEDED',
        loginMethodType: payload.loginMethodType,
        login2FAStatus: apiData.status2FA ? apiData.status2FA : undefined,
      },
      user: apiData.user,
      settings: { ...apiData.settings, serverId: apiData.serverId },
      timeZones: apiData.timeZones,
      diagnosticStatus: apiData.diagnosticStatus,
      disabled: apiData.disabled,
      disable_reason: apiData.disable_reason,
      passwordAge: apiData.passwordAge,
      hardwareTypeList: apiData.hardwareTypeList,
      preferences: {
        ...(isSCDF ? scdfDefaultPreferences : defaultPreferences),
        ...payload.preferences,
      },
      rememberedUsername: '',
      debug: payload.debug,
      vehicleIdToSelectOnMapAfterLogin: payload.vehicleIdToSelectOnMap,
      refreshTokenTimeoutId:
        payload.refreshTokenTimeoutId ?? state.refreshTokenTimeoutId,
    }
  }
  if (tokenLoginSucceeded.match(action)) {
    const { payload } = action

    return {
      ...state,
      loginApiData: {
        status: 'SUCCEEDED',
        loginMethodType: 'token',
        login2FAStatus: undefined,
      },
      user: payload.user,
      settings: { ...payload.settings, serverId: undefined },
      timeZones: emptyState.timeZones,
      diagnosticStatus: {},
      disabled: undefined,
      disable_reason: '',
      passwordAge: '',
      preferences: emptyState.preferences,
      rememberedUsername: '',
      debug: false,
      hardwareTypeList: undefined,
    }
  }
  if (setUserLocale.match(action)) {
    return {
      ...state,
      locale: action.payload.locale,
    }
  }

  if (setDashboardVehicleDisplayName.match(action)) {
    return {
      ...state,
      settings: {
        ...state.settings,
        dashboardVehicleDisplayName: action.payload,
      },
    }
  }
  if (startedPreLoginFetch.match(action)) {
    return {
      ...state,
      preLoginQuery: {
        // keep the status (similar to react-query). This allows us to do specific login in case it's first time loading, etc
        status: state.preLoginQuery.status,
        fetchStatus: 'fetching',
      },
    }
  }
  if (failedPreLoginFetch.match(action)) {
    return {
      ...state,
      preLoginQuery: {
        status: 'error',
        fetchStatus: 'idle',
      },
    }
  }

  if (sisenseLoginSuccess.match(action)) {
    return {
      ...state,
      settings: {
        ...state.settings,
        sisenseApi: action.payload.sisenseApi,
      },
    }
  }
  if (logoutFailed.match(action)) {
    return { ...state, logoutMutation: { status: 'error' } }
  }
  if (startLogOut.match(action)) {
    return {
      ...state,
      settings: {
        ...state.settings,
        styleProperties: state.settings.styleProperties || {},
      },
      rememberedUsername: state.rememberedUsername,
      locale: state.locale,
      logoutMutation: { status: 'pending' },
      countriesWebsites: state.countriesWebsites,
    }
  }
  if (batchReduxStateUpdatesFromSaga.match(action)) {
    return action.payload.userState ? { ...state, ...action.payload.userState } : state
  }
  if (receivePreLoginData.match(action)) {
    const { payload } = action
    return {
      ...state,
      languageList: payload.languageList,
      locale: payload.locale,
      ctCountriesList: payload.ctCountries,
      federatedLogins: payload.federatedLogins,
      countriesWebsites: payload.countriesWebsites,
      settings: {
        ...state.settings,
        styleProperties: payload.styleProperties,
      },
      preLoginQuery: {
        status: 'success',
        fetchStatus: 'idle',
      },
    }
  }
  if (initKeyIdbState.match(action) || setKeyIdbStateOnRedux.match(action)) {
    const { key, value } = action.payload

    return {
      ...state,
      idbStates: {
        ...state.idbStates,
        [key]: value,
      },
    }
  }
  if (deleteKeyIdbSyncState.match(action)) {
    const { key } = action.payload
    const newIdbStates = { ...state.idbStates }
    delete newIdbStates[key]
    return {
      ...state,
      idbStates: newIdbStates,
    }
  }

  switch (action.type) {
    case resetUserStateBeforeAttemptingALogin.type: {
      const settings = state.settings
      const preLoginSettings: SuccessfulPreLoginResponse['settings'] = {
        basicLogin: settings.basicLogin,
        mobileStoreLinks: settings.mobileStoreLinks,
        googlePlayLink: settings.googlePlayLink,
        appStoreLink: settings.appStoreLink,
        styleProperties: settings.styleProperties as FixMeAny,
        defaultCountry: settings.defaultCountry,
        defaultissuingcountry: settings.defaultissuingcountry,
        eld: settings.eld,
        disableThirdPartyLogging: settings.disableThirdPartyLogging as boolean,
        useFederationLoginOnly: settings.useFederationLoginOnly,
        enableWelcomePage: settings.enableWelcomePage,
        contactEmailEnabled: settings.contactEmailEnabled,
        contactSmsEnabled: settings.contactSmsEnabled,
        contactSupportEmail: settings.contactSupportEmail,
      }

      return {
        ...state,
        ...emptyState,
        allowEditSensor: state.allowEditSensor,
        loginApiData: { status: 'PROCESSING' },
        settings: preLoginSettings,
      }
    }

    case SUB_USER_LOGIN: {
      return {
        ...state,
        loginApiData: { status: 'PROCESSING' },
      }
    }

    case LOGGED_OUT: {
      return {
        ...state,
        ...emptyState,
        logoutMutation: { status: 'success' },
      }
    }

    case PASSWORD_UPDATE_STATUS: {
      return {
        ...state,
        passwordUpdateStatus: action.payload,
      }
    }

    case ON_SAVE_PROFILE: {
      if (!state.user) {
        return state
      }
      return {
        ...state,
        user: {
          ...state.user,
          companyName: action.payload.settings.companyName,
        },
        settings: {
          ...state.settings,
          ...action.payload.settings,
        },
      }
    }
    case SAVE_PREFERENCE: {
      return {
        ...state,
        preferences: {
          ...state.preferences,
          [action.payload.key]: action.payload.value,
        },
      }
    }
    case SET_SYSTEM_STATE_MESSAGE: {
      return {
        ...state,
        systemStateMessage: action.payload.systemStateMessage,
      }
    }
    case UPDATE_STYLE_SETTINGS: {
      return {
        ...state,
        settings: {
          ...state.settings,
          styleProperties: action.payload.styleProperties,
        },
      }
    }
    case RECEIVE_USER_SETTINGS: {
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload.settings,
        },
      }
    }
    case FETCH_ENGINE_SUCCESS: {
      return {
        ...state,
        diagnostic: action.payload.diagnostic,
        diagnosticFaulty: action.payload.diagnosticFaulty,
        diagnosticHealthy: action.payload.diagnosticHealthy,
      }
    }
    case SET_DASHBOARD_INDUSTRY: {
      return {
        ...state,
        settings: {
          ...state.settings,
          dashboardIndustry: action.payload,
        },
      }
    }

    default: {
      return state
    }
  }
}

// Action Creators
export function login(
  payload: (
    | {
        type: 'otp'
        username: string
        otp: string
      }
    | {
        type: 'sso'
        sso: string
        t: string
      }
    | ({ type: 'credentials_login' } & LoginSchema)
    | ReturnType<typeof federatedLogin>['payload']
  ) & { vehicle?: string; vehicleIdToSelectOnMap?: VehicleId },
) {
  return { type: LOG_IN, payload }
}

export function federatedLogin(data: { federatedResponse: string }) {
  return {
    type: FEDERATED_LOG_IN,
    payload: { type: 'federated' as const, ...data },
  }
}

export function subUserLogin(user: { userId: string; name: string }) {
  return {
    type: SUB_USER_LOGIN,
    payload: user,
  }
}

export function tokenLogin({
  vehicleId,
  userId,
  token,
  clientId,
  getSharedVehicleData = false,
}: {
  vehicleId: string
  userId: string
  token: string
  clientId?: string
  getSharedVehicleData?: boolean
}) {
  return {
    type: TOKEN_LOG_IN,
    payload: {
      vehicleId,
      userId,
      token,
      clientId,
      getSharedVehicleData,
    },
  }
}

export const logout = createAction<{
  meta?: { logoutFromOtherOpenTabs?: boolean; preventRedirect?: boolean }
}>('logout_action')

export function register(formData: Record<string, FixMeAny>) {
  return {
    type: REGISTER,
    payload: {
      ...formData,
    },
  }
}

export function saveProfile(
  formData: UserSchema['profile'] & UserSchema['settings'] & { imgSrc: string | null },
) {
  return {
    type: SAVE_PROFILE,
    payload: { formData },
  }
}

export function savePreferences<Key extends keyof UserPreferences>(
  key: Key,
  value: UserPreferences[Key],
) {
  return {
    type: SAVE_PREFERENCE,
    payload: { key, value },
  }
}

export function updateUserImage(base64Image: string | undefined) {
  return {
    type: UPDATE_USER_IMAGE,
    payload: { base64Image },
  }
}

export function submitHelpRequest(request: Ct_fleet_contact_us.ApiFnInput) {
  return {
    type: SUBMIT_HELP_REQUEST,
    payload: { request },
  }
}

export function toggleUserSsoHash(status: boolean) {
  return {
    type: TOGGLE_SSO_HASH,
    payload: { status },
  }
}

export function UserClearData() {
  return {
    type: SUBMIT_CLEAR_FUEL,
  }
}

export function setUserPassword(password: string, currentPassword: string) {
  return {
    type: SET_USER_PASSWORD,
    payload: {
      password,
      currentPassword,
    },
  }
}

// Selectors
/**
 * @deprecated You should use IANA names instead.
 */
export const getAppTZOffset = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
) => state.user.settings.timezoneOffset
export const getIsTime24 = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
) => state.user.settings.timeFormat24h

export const getUser = (state: AppState) => state.user.user

/** Gets the user __as if__ he is already authenticated.
 *
 *  __IMPORTANT:__ If you need to access certain account user properties, use `getAuthenticatedUserAsAccountUser` instead.
 *
 *  NOTE: This is not a pattern that we want to encourage! However, it's one of the rare cases where it is useful and it makes sense.
 *  Otherwise, we would have to check for "null" every time we wanted to get the authenticated user (even if we know that in the context of our usage he has to be authenticated).
 *  Nonetheless, always be aware that the user "correct" selector is getUser
 */
export const getAuthenticatedUser = (state: AppState): LoggedInBaseUser => {
  const user = getUser(state)
  if (!user) {
    const errMsg =
      '[Cartrack] If have used __getAuthenticatedUser__ selector when user is not defined'
    if (ENV.DEPLOYMENT_ENV === 'production') {
      console.error(errMsg)
      return user as unknown as User // Do not throw in production to avoid breaking the app immediately
    }
    throw new Error(errMsg) // Fail early in test environments to fix problem quickly
  }

  return user
}

/** Gets the user __as if__ he is already authenticated.
 *  Most typical "getUser" selector.
 *  NOTE: This is only to be used when we are sure that the user is an account user and NOT a token user.
 *  Token users (made using ct_token_login) are typically used in shared vehicle pages or trips pages, where the user may or may not have a cartrack account.
 */
export const getAuthenticatedUserAsAccountUser = (
  state: AppState,
): LoggedInAccountUser => {
  const user = getUser(state)
  if (!user) {
    const errMsg =
      '[Cartrack] If have used __getAuthenticatedUserAsAccountUser__ selector when user is not defined'
    if (ENV.DEPLOYMENT_ENV === 'production') {
      console.error(errMsg)
      return user as unknown as LoggedInAccountUser // Do not throw in production to avoid breaking the app immediately
    }
    throw new Error(errMsg) // Fail early in test environments to fix problem quickly
  }
  if (user.isTokenAccount) {
    const errMsg =
      '[Cartrack] - You have used __getAuthenticatedUserAsAccountUser__ for a token account.'
    if (ENV.DEPLOYMENT_ENV === 'production') {
      console.error(errMsg)
      return user as unknown as LoggedInAccountUser // Do not throw in production to avoid breaking the app immediately
    }
    throw new Error(errMsg)
  }

  return user
}

/** Since user.id is always the main/admin account id, sub user accounts will have that same id. This makes user.id non unique globally.
 *
 *  Concatenating user.id with user.cuid (client user id), which is filled in current user is a sub user, will generate a unique id
 */
export const getAuthenticatedUserUniqueId = (state: AppState) => {
  const user = getAuthenticatedUser(state)
  return `userId:${user.id}_cuid:${user.cuid}` as AuthenticatedUserUniqueId
}

export const getIsFetchingPreLogin = (state: AppState) =>
  state.user.preLoginQuery.fetchStatus === 'fetching'

export const getPreLoginQuery = (state: AppState) => state.user.preLoginQuery

export const getLoginApiData = (state: AppState) => state.user.loginApiData

export const getSystemStateMessage = (state: AppState) => state.user.systemStateMessage

export const getHardwareTypeList = (state: AppState) => state.user.hardwareTypeList

export const getClustersEnabled = (state: AppState) =>
  getSettings_UNSAFE(state).mapClustersEnabled ?? false

export const getMaxNumberOfClusterAllowed = (state: AppState) =>
  Number(getSettings_UNSAFE(state).maxMapClusters ?? 5)

export const getMaxNumberOfVehiclesOnClusterAllowed = (state: AppState) =>
  Number(getSettings_UNSAFE(state).mapClustersMaxVehicles ?? 5)

export const getVisionWatchVideoStreamSetting = (state: AppState) => {
  const { visionWatchVideoStream } = getSettings(state)
  return parseBooleanUserSetting(visionWatchVideoStream, {
    defaultValueWhenNotValid: false,
  })
}

export const getMapVehiclesPositionsRefreshRateInMs = (state: AppState) => {
  const mapVehiclesPositionsRefreshRateInSeconds =
    state.user.settings.mapVehiclesPositionsRefreshRate ??
    MAP_VEHICLES_POSITIONS_DEFAULT_REFRESH_RATE_IN_SECONDS

  return (mapVehiclesPositionsRefreshRateInSeconds as number) * 1000
}

export const getRequestIndustryPopupIntervalInSecondsSetting = (state: AppState) =>
  Number(
    getSettings(state).requestIndustryPopupIntervalInSeconds ??
      daysTo(7, { to: 'seconds' }),
  )

export const getRequestNpsScoreDismissIntervalInSecondsSetting = (
  state: AppState,
): number => {
  const { npsPopupRepeatInSeconds } = getSettings(state)
  if (isNil(npsPopupRepeatInSeconds)) {
    return daysTo(60, { to: 'seconds' })
  }

  return Number(npsPopupRepeatInSeconds)
}

export const getRequestNewNpsScoreIntervalInSecondsSetting = (
  state: AppState,
): number => {
  const { npsScoreRepeatInSeconds } = getSettings(state)
  if (isNil(npsScoreRepeatInSeconds)) {
    return daysTo(360, { to: 'seconds' })
  }

  return Number(npsScoreRepeatInSeconds)
}

export const getVehiclesShareLocationSetting = (state: AppState) =>
  getSettings(state).vehiclesShareLocation

export const getListSetting = (state: AppState) => {
  const { list } = getSettings(state)
  return parseBooleanUserSetting(list, { defaultValueWhenNotValid: false })
}

export const getActualListVehiclesSetting = (state: AppState) => {
  const { listVehicles } = getSettings(state)
  return parseBooleanUserSetting(listVehicles, { defaultValueWhenNotValid: false })
}
/**
 * List vehicle setting depends on list to actually see if the current user has access to vehicles
 * @returns
 */
export const getListVehiclesPermission = (state: AppState) =>
  getListSetting(state) && getActualListVehiclesSetting(state)

export const getGoogleMapApiKey = (state: AppState) =>
  getSettings(state).gmapApiKey as string

export const getDriverMandatoryLicenseSetting = (state: AppState) => {
  const { driversMandatoryLicense } = getSettings(state)

  return parseBooleanUserSetting(driversMandatoryLicense, {
    defaultValueWhenNotValid: false,
  })
}

const defaultCountrySettingStandardizedSchema = z.enum([
  'ZA',
  'SG',
  'AO',
  'BW',
  'FR',
  'ES',
  'HK',
  'ID',
  'KE',
  'KH',
  'AE', // Transformed from ME
  'ZM',
  'MW',
  'MY',
  'MZ',
  'NA',
  'NG',
  'NZ',
  'AU',
  'PH',
  'PL',
  'PT',
  'RW',
  'SG',
  'SZ',
  'TH',
  'TZ',
  'UG',
  'US',
  'ZM',
  'ZW',
  'VN',
])
type StandardizedDefaultCountrySetting = z.infer<
  typeof defaultCountrySettingStandardizedSchema
>
export const getStandardizedDefaultCountrySetting = createSelectorWithStrictMode(
  getSettings,
  ({ defaultCountry }): StandardizedDefaultCountrySetting | null => {
    if (isNonEmptyTrimmedString(defaultCountry)) {
      const upperCased = defaultCountry.trim().toUpperCase()
      const parseResult = defaultCountrySettingStandardizedSchema.safeParse(upperCased)
      if (parseResult.success) {
        return parseResult.data
      }
      return upperCased === 'ME' ? 'AE' : null
    }
    return null
  },
)

export const getCoachingTemplateSetting = (
  state: AppState,
): 'new_completed' | 'new_tocoach_completed' => {
  const coachingTemplate = getSettings(state).coachingTemplate

  if (typeof coachingTemplate === 'string') {
    switch (coachingTemplate) {
      case 'new_completed': {
        return coachingTemplate
      }
      case 'new_tocoach_completed': {
        return coachingTemplate
      }
      default: {
        return 'new_completed'
      }
    }
  }

  return 'new_completed'
}

export const getCoachingCanAssignDrivers = (state: AppState) => {
  const { coachingCanAssignDrivers } = getSettings(state)
  return parseBooleanUserSetting(coachingCanAssignDrivers, {
    defaultValueWhenNotValid: false,
  })
}

export const getCoachingShowNoCoachingNeededButton = (state: AppState) => {
  const { coachingShowNoCoachingNeededButton } = getSettings(state)
  return parseBooleanUserSetting(coachingShowNoCoachingNeededButton, {
    defaultValueWhenNotValid: false,
  })
}

export const getPrivacySetting = (state: AppState) =>
  getSettings(state).privacy as boolean
export const getVisionSetting = (state: AppState): boolean =>
  getSettings_UNSAFE(state).vision ?? false

export const getListDvirsSetting = (state: AppState) =>
  getSettings(state).listDvirs as boolean

export const getVisionDiscoverMoreUrl = (state: AppState) => {
  const discoverMoreUrl = getSettings(state).visionDiscoverMoreUrl as string | ''

  return isEmpty(discoverMoreUrl) ? null : discoverMoreUrl
}

export const getVisionDiscoverMoreMailto = (state: AppState) => {
  const discoverMoreMailto = getSettings(state).visionDiscoverMoreMailto as string | ''

  return isEmpty(discoverMoreMailto) ? null : discoverMoreMailto
}

export const getVisionDiscoverMoreMethod = (state: AppState) => {
  const discoverMoreMethod = getSettings(state).visionDiscoverMoreMethod as
    | 'off'
    | 'mailto'
    | 'url'

  return isEmpty(discoverMoreMethod) ? 'off' : discoverMoreMethod
}

export const getVisionDiscoverMoreMethodValue = createSelector(
  getVisionDiscoverMoreMethod,
  getVisionDiscoverMoreMailto,
  getVisionDiscoverMoreUrl,
  (method, mailToValue, urlValue) => {
    switch (method) {
      case 'mailto': {
        return { method: 'mailto', value: mailToValue } as const
      }
      case 'url': {
        return { method: 'url', value: urlValue } as const
      }
      default: {
        return { method: 'off' } as const
      }
    }
  },
)

export const getVisionMaxVideoStreams = (state: AppState) => {
  const visionMaxVideoStreams = getSettings(state).visionMaxVideoStreams

  return isNil(visionMaxVideoStreams) ? 16 : Number(visionMaxVideoStreams)
}

export const getCoachingEventsColumnMenuItemDriversPreset = (
  state: AppState,
): 'all_drivers' | 'my_coaching_drivers' | false => {
  const coachingEventsColumnMenuItemDriversPreset =
    getSettings(state).coachingEventsColumnMenuItemDriversPreset

  switch (coachingEventsColumnMenuItemDriversPreset) {
    case 'all_drivers': {
      return 'all_drivers'
    }
    case 'my_coaching_drivers': {
      return 'my_coaching_drivers'
    }
    default: {
      return false
    } // Do not shown the menu item by default
  }
}

export const getListGeofencesSetting = (state: AppState) => {
  const { listGeofences } = getSettings(state)

  return parseBooleanUserSetting(listGeofences, {
    defaultValueWhenNotValid: false,
  })
}

export const getSsoHashSetting = (state: AppState) =>
  state.user.settings.ssoHash as FixMeAny
export const getRememberedUsername = (state: AppState) => state.user.rememberedUsername
export const getIsDebugging = (state: AppState) => state.user.debug
export const getAllowEditSensors = (state: AppState) => state.user.allowEditSensor
export const getLanguageListAsOptions = createSelectorWithStrictMode(
  (state: AppState) => state.user.languageList,
  (list) =>
    list?.options.map((o) => ({
      label: o.name,
      value: o.value,
      default: o.default,
    })) ?? [],
)
export const getCtCountriesList = (state: AppState) => state.user.ctCountriesList
export const getFederatedLogins = (state: AppState) => state.user.federatedLogins
export const getAllowMaxSpeed = (state: AppState) =>
  state.user.settings.vehicle_enable_max_speed as FixMeAny

export const getDisabled = (state: AppState) => state.user.disabled
export const getDisableReason = (state: AppState) => state.user.disable_reason ?? ''

export const getPasswordAge = (state: AppState) => state.user.passwordAge
export const getPasswordUpdateStatus = (state: AppState) =>
  state.user.passwordUpdateStatus
export const getLocale = (state: AppState) => state.user.locale

export const getGoogleMapsLanguageCodeFromUserLocale = createSelector(
  getLocale,
  (locale): GoogleMapsLocaleCode | undefined => {
    if (!locale) {
      return locale
    }
    const definedLocale = locale
    return match<typeof definedLocale, GoogleMapsLocaleCode>(definedLocale)
      .with('ar', () => 'ar')
      .with('en', () => 'en')
      .with('en-ZA', () => 'en')
      .with('en-SG', () => 'en')
      .with('es', () => 'es')
      .with('es-MX', () => 'es-419')
      .with('fr', () => 'fr')
      .with('id', () => 'id')
      .with('ja-JP', () => 'ja')
      .with('km', () => 'km')
      .with('ms', () => 'ms')
      .with('pl', () => 'pl')
      .with('pt-MZ', () => 'pt')
      .with('pt-PT', () => 'pt-PT')
      .with('vi', () => 'vi')
      .with('th', () => 'th')
      .with('zh', () => 'zh')
      .with('zh-Hans-HK', () => 'zh-HK')
      .with('fil', () => 'fil')
      .with('en-NZ', () => 'en')
      .exhaustive()
  },
)

export const getIsLoggingOut = (state: AppState) =>
  state.user.logoutMutation.status === 'pending'

export const getLogoutMutationStatus = (state: AppState) =>
  state.user.logoutMutation.status

export const getTimeZonesOptions = createSelector(getTimeZones, (timeZones) =>
  (timeZones ?? []).map((t) => ({
    label: t.description,
    name: t.description,
    value: t.id,
  })),
)

export const getUserIANATimezone = (state: AppState) =>
  getSettings(state).defaultTimezone as string | undefined

export const getRucLicenseAlmostExpiredInKmSetting = (state: AppState): number => {
  const rucLicenseAlmostExpiredInKm = getSettings(state).rucLicenseAlmostExpiredInKm
  return isNilOrEmptyTrimmedStringOrFalseish(rucLicenseAlmostExpiredInKm)
    ? 200
    : Number(rucLicenseAlmostExpiredInKm)
}

export type UserStyleSettings = {
  styleAppName: string
  // Sidebar settings
  styleNavbarColour: string | null | ''
  styleSidebarMenuFooterLogoPath: string
  styleSidebarMenuFooterIconPath: string
  styleSidebarMenuActiveFontColour: string
  styleSidebarMenuInactiveFontColour: string
  styleSidebarMenuHoverFontColour: string
  styleSidebarMenuHoverColour: string
  styleSidebarMenuActiveColour: string
  styleSidebarSubMenuInactiveFontColour: string

  styleMenuActiveFontColour: string | null | ''
  styleMenuInactiveFontColour: string | null | ''

  // Login settings
  styleMainLogoWidthPx?: `${number}` | number | '' | null
  styleLoginFooterHeight?: `${number}` | number | '' | null
  styleLoginLanguage?: boolean | null | ''
  styleLoginSignUp?: boolean | null | ''
  styleLoginStayLoggedIn?: boolean | null | ''
  styleLoginLogoInsideBox?: boolean | null | ''
  styleLoginFooterLogo?: string | null | ''
  styleLoginFooterBackgroundColor?: string | null | ''
  styleLoginForgotUsername?: boolean | null | ''
  styleLoginModalFooter?: string | null | ''
  styleLogoPoweredByType?: unknown
  styleLogoPoweredByType3Label: unknown
}

type UserSettingsStyleProperties = Record<string, FixMeAny> & {
  karooUiTheme?: ParsedKarooUiCustomizableTheme
} & UserStyleSettings

export const getUserSettingStyleProperties = createSelectorWithStrictMode(
  getSettings,
  (settings): UserSettingsStyleProperties => {
    const { styleProperties } = settings
    if (typeof styleProperties !== 'object' || isNil(styleProperties)) {
      return {} as UserSettingsStyleProperties
    }

    const stylePropertiesCast = styleProperties as Record<string, FixMeAny>

    return {
      ...stylePropertiesCast,
      styleAppName: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleAppName,
      )
        ? stylePropertiesCast.styleAppName
        : defaultStyles.styleAppName,

      styleNavbarColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleNavbarColour,
      )
        ? stylePropertiesCast.styleNavbarColour
        : defaultStyles.styleNavbarColour,

      styleSidebarMenuFooterLogoPath: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuFooterLogoPath,
      )
        ? stylePropertiesCast.styleSidebarMenuFooterLogoPath
        : defaultStyles.styleSidebarMenuFooterLogoPath,

      styleSidebarMenuFooterIconPath: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuFooterIconPath,
      )
        ? stylePropertiesCast.styleSidebarMenuFooterIconPath
        : defaultStyles.styleSidebarMenuFooterIconPath,

      styleSidebarMenuActiveFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuActiveFontColour,
      )
        ? stylePropertiesCast.styleSidebarMenuActiveFontColour
        : defaultStyles.styleSidebarMenuActiveFontColour,

      styleSidebarMenuInactiveFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuInactiveFontColour,
      )
        ? stylePropertiesCast.styleSidebarMenuInactiveFontColour
        : defaultStyles.styleSidebarMenuInactiveFontColour,

      styleSidebarMenuHoverFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuHoverFontColour,
      )
        ? stylePropertiesCast.styleSidebarMenuHoverFontColour
        : defaultStyles.styleSidebarMenuHoverFontColour,

      styleSidebarMenuHoverColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuHoverColour,
      )
        ? stylePropertiesCast.styleSidebarMenuHoverColour
        : defaultStyles.styleSidebarMenuHoverColour,

      styleSidebarMenuActiveColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarMenuActiveColour,
      )
        ? stylePropertiesCast.styleSidebarMenuActiveColour
        : defaultStyles.styleSidebarMenuActiveColour,

      styleSidebarSubMenuInactiveFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleSidebarSubMenuInactiveFontColour,
      )
        ? stylePropertiesCast.styleSidebarSubMenuInactiveFontColour
        : defaultStyles.styleSidebarSubMenuInactiveFontColour,

      styleMenuActiveFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleMenuActiveFontColour,
      )
        ? stylePropertiesCast.styleMenuActiveFontColour
        : defaultStyles.styleMenuActiveFontColour,

      styleMenuInactiveFontColour: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleMenuInactiveFontColour,
      )
        ? stylePropertiesCast.styleMenuInactiveFontColour
        : defaultStyles.styleMenuInactiveFontColour,

      styleLogoPoweredByType3Label: isNonEmptyTrimmedStringAndNotFalseish(
        stylePropertiesCast.styleLogoPoweredByType3Label,
      )
        ? stylePropertiesCast.styleLogoPoweredByType3Label
        : defaultStyles.styleLogoPoweredByType3Label,
    }
  },
)

export const getInputFieldColors = createSelectorWithStrictMode(
  getUserSettingStyleProperties,
  (userSettingStyleProperties) => {
    const {
      styleInputfieldColourActive,
      styleInputfieldColourSelection,
      styleInputfieldColourHover,
      styleInputfieldIconColourStandard,
      styleInputfieldIconColourActive,
    } = userSettingStyleProperties

    return {
      styleInputfieldColourActive,
      styleInputfieldColourSelection,
      styleInputfieldColourHover,
      styleInputfieldIconColourStandard,
      styleInputfieldIconColourActive,
    }
  },
)

export const getUserSettingsStyleMainLogoWidthPx = (state: AppState) => {
  const { styleMainLogoWidthPx } = getUserSettingStyleProperties(state)

  return styleMainLogoWidthPx === '' || isNil(styleMainLogoWidthPx)
    ? null
    : Number(styleMainLogoWidthPx)
}

export const getKarooUiTheme = (state: AppState) =>
  getUserSettingStyleProperties(state).karooUiTheme

export const getUserStyleAppName = (state: AppState): string => {
  const { styleAppName } = getUserSettingStyleProperties(state)
  return isNilOrEmptyString(styleAppName) ? defaultStyles.styleAppName : styleAppName
}

/**
 * Very similar to getUserStyleAppName, but this one is used to represent the entity that provide the platform.
 * 99% of the cases it's Cartrack, but for some OEMs customers that have a custom name, it can be different, e.g: BMW, Toyota, etc.
 */
export const getUserEnterpriseEntityName = (state: AppState): string => {
  const { styleAppName } = getUserSettingStyleProperties(state)
  return isNilOrEmptyString(styleAppName) ? 'Cartrack' : styleAppName
}

export const getClientLoginLogoPathMeta = createSelectorWithStrictMode(
  getUserSettingStyleProperties,
  ({ styleCustomerMainLogo }) =>
    parseImagePathUserSetting(styleCustomerMainLogo, {
      fallbackPath: defaultStyles.styleCustomerMainLogo,
    }),
)

export const getClientMenuLogoPathMeta = createSelectorWithStrictMode(
  getUserSettingStyleProperties,
  ({ styleCustomerMenuLogo }) =>
    parseImagePathUserSetting(styleCustomerMenuLogo, {
      fallbackPath: defaultStyles.styleCustomerMenuLogo,
    }),
)

export const getClientSidebarMenuHeaderIconPathMeta = createSelectorWithStrictMode(
  getUserSettingStyleProperties,
  ({ styleSidebarMenuHeaderIconPath }) =>
    parseImagePathUserSetting(styleSidebarMenuHeaderIconPath, {
      fallbackPath: defaultStyles.styleSidebarMenuHeaderIconPath,
    }),
)

export const getEngineAlertCount = (state: AppState) => {
  if (isEmpty(state.user.diagnosticStatus)) {
    return {}
  }

  const { out_diagnostic_error_count: alertCount } = state.user.diagnosticStatus

  return {
    count: isEmpty(alertCount) ? 0 : Number.parseInt(alertCount, 10),
  }
}

export const getThirdPartyUser = (state: AppState) => {
  const { loginApiData } = state.user

  return loginApiData.status === 'THIRD_PARTY_USER' ? loginApiData : null
}

export const getTwoFALoginData = (state: AppState) => {
  const { loginApiData } = state.user

  return loginApiData.status === 'TWO_FACTOR_AUTHENTICATION' ? loginApiData : null
}

export const getDashboardVehicleDisplayNameSetting = (
  state: AppState,
): VehicleDisplayName => {
  const displayName = getSettings_UNSAFE(state).dashboardVehicleDisplayName
  return displayName ?? 'registration'
}

// Time functions

/**
 * Returns a date string in the correct timezone to be used with FormattedUserDate
 * @param state - redux state
 * @param dateTime - Unix timestamp in ms or any valid DateTime string that can be used by moment()
 * @param preserveTime - determines whether the time starting from hours, minutes etc should be retained when changing the timezone
 * @returns Moment object with formatted date-time with server time zone offset
 */
export const getServerTime = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
  dateTime?: MomentInput,
  preserveTime = false,
) => {
  const dateTimeMoment = moment(dateTime)
  const tzOffset = dateTime ? (dateTimeMoment as FixMeAny)._tzm : undefined
  // eslint-disable-next-line no-nested-ternary
  const serverOffset = !isNil(tzOffset)
    ? tzOffset
    : !isNil(getAppTZOffset(state))
      ? getAppTZOffset(state)
      : 480

  if (preserveTime) {
    const localOffset = dateTimeMoment.utcOffset()
    dateTimeMoment.subtract((serverOffset - localOffset) / 60, 'hours')
  }

  return dateTimeMoment.utcOffset(serverOffset)
}

/**
 * @deprecated Use luxon instead.
 * Returns a date string in the correct format to be used in Firefox with new Date(date)
 * @param state - redux state
 * @param date - Unix timestamp in ms or any valid DateTime string that can be used by moment()
 * @return formatted date with time zone offset
 */
export const getDateWithTZ = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
  date: MomentInput,
) => {
  const dateTimeMoment = moment(date)
  const tzOffset = date ? (dateTimeMoment as FixMeAny)._tzm : undefined

  return dateTimeMoment
    .utcOffset(tzOffset || getAppTZOffset(state))
    .format('YYYY-MM-DD HH:mm:ssZZ') // Format to work in Firefox
}

/**
 * @deprecated Use luxon instead.
 * Returns a date string in the correct timezone to be used with FormattedUserDate
 * @param state - redux state
 * @param date - Unix timestamp in ms or any valid DateTime string that can be used by moment()
 * @param includeDay - option to include the day in full at the beginning
 * @param intlFormat - date format obtained from react-intl
 * @return {string} formatted date with time zone offset
 */
export const getFormattedDateWithTZ = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
  date: MomentInput,
  includeDay: boolean,
  intlFormat: string,
) => {
  const formatRule = (includeDay ? 'dddd ' : '') + intlFormat

  const dateTimeMoment = moment(date)
  const tzOffset = date ? (dateTimeMoment as FixMeAny)._tzm : undefined

  return dateTimeMoment.utcOffset(tzOffset || getAppTZOffset(state)).format(formatRule)
}

/**
 * @deprecated Use luxon instead.
 * Returns a time string in the correct timezone to be used with FormattedUserTime
 * @param state - redux state
 * @param dateTime - Unix timestamp in ms or any valid DateTime string that can be used by moment()
 * @param forcedTZOffset - Timezone offset explicitly specified when the dateTime doesn't have the required timezone offset included
 * @param includeSeconds - Determines whether the time should be displayed with seconds or not
 * @return formatted time with time zone offset
 */
export const getFormattedTimeWithTZ = (
  state: FixMeAny /** Using AppState causes types circular dependency. Will be investigated in the future */,
  dateTime: MomentInput,
  forcedTZOffset?: number,
  includeSeconds = false,
) => {
  const seconds = includeSeconds ? ':ss' : ''
  const formatRule = getIsTime24(state) ? `HH:mm${seconds}` : `hh:mm${seconds} A`

  const dateTimeMoment = moment(dateTime)
  let tzOffset = forcedTZOffset
  if (!tzOffset) {
    tzOffset = dateTime ? (dateTimeMoment as FixMeAny)._tzm : undefined
  }

  return dateTimeMoment.utcOffset(tzOffset || getAppTZOffset(state)).format(formatRule)
}

export const getSpeedUnit = ({
  user: { settings: userSettings },
}: AppState): 'MPH' | 'KM/H' => (isTrue(userSettings.distanceInMiles) ? 'MPH' : 'KM/H')

export const getIsUserDistanceInMiles = (state: AppState) => {
  const { distanceInMiles } = getSettings_UNSAFE(state)

  return parseBooleanUserSetting(distanceInMiles, {
    defaultValueWhenNotValid: false,
  })
}

export const getEnableWelcomePageSetting = (state: AppState) => {
  const enableWelcomePage = getSettings(state).enableWelcomePage

  switch (enableWelcomePage) {
    case 'true':
    case true: {
      return true
    }
    case 'false':
    case false: {
      return false
    }
    default: {
      return true
    }
  }
}

export const getUserCurrencySymbol = (state: AppState) =>
  getSettings_UNSAFE(state).currencySymbol

// defaultissuingcountry should have values according to https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements
export const getISO3166Alpha2CountryCode = (state: AppState) =>
  getSettings_UNSAFE(state).defaultissuingcountry as ISO3166_1Alpha2CountryCode

export const getMapVehicleLabels = (state: AppState) =>
  getPreferences(state).mapVehicleLabels

export const getIsSomeMapVehicleLabelAvailable = createSelector(
  getMapVehicleLabels,
  (labels) => labels && Object.values(labels).includes(true),
)

export const getCarpoolDynamicFeatureName = (state: AppState) =>
  (getSettings(state).carpoolAppName as string) || 'Carpool'

export const getSpecialLicensesDynamicFeatureName = (state: AppState) =>
  ((getSettings(state).specialLicensesLabel as string) || 'Special Licenses').replace(
    '-',
    ' ',
  )

export const doesCurrentUserSeesNewMiFleetImports = (state: AppState) =>
  getSettings_UNSAFE(state).mifleetImportDataV2

export const doesCurrentUserHaveAccessFromSetting = (
  state: AppState,
  /** Access setting with bool value */
  setting: string,
) => {
  const isCurrentUserAuthenticated = !isEmpty(getUser(state))

  if (isCurrentUserAuthenticated) {
    const settings = getSettings(state)
    const value = settings[setting]

    let errorMessage
    if (isEmpty(settings) === false) {
      if (value === undefined) {
        errorMessage = `[Cartrack] - There is no setting with name ${JSON.stringify(
          setting,
        )}`
      } else if (isBoolean(value) === false) {
        errorMessage = `[Cartrack] - Access type settings should be of type bool. The value for setting ${JSON.stringify(
          setting,
        )} was ${JSON.stringify(value)}`
      }
    }

    if (errorMessage !== undefined) {
      // It will be logged to sentry but not crash the app since it's production
      console.error(errorMessage)
    }

    return value === true
  }

  return false
}

export const getMapZoomOptions = createSelector(getSettings_UNSAFE, (settings = {}) => {
  const { minimumMapZoom, maximumMapZoom } = settings
  const minZoom: number = minimumMapZoom || MAP_MIN_ZOOM
  const maxZoom: number = maximumMapZoom || MAP_MAX_ZOOM
  return {
    minZoom,
    maxZoom,
    mapClusterMaxZoom: maximumMapZoom ? maxZoom - 1 : MAP_CLUSTER_MAX_ZOOM,
  }
})

export const getTemperatureUnitSetting = (state: AppState): 'F' | 'C' => {
  const { temperatureInFahrenheit } = getSettings(state)

  const parsedValue = parseBooleanUserSetting(temperatureInFahrenheit, {
    defaultValueWhenNotValid: false,
  })

  return parsedValue ? 'F' : 'C'
}

export type TemperatureUnitSetting = ReturnType<typeof getTemperatureUnitSetting>

/**
 *
 * This setting should probably be replaced with a more general one like liquidVolumeUnit
 */
export const getFuelUnitSetting = (state: AppState): 'us_gallon' | 'liter' => {
  const { fuelInUsGallon } = getSettings(state)

  const parsedValue = parseBooleanUserSetting(fuelInUsGallon, {
    defaultValueWhenNotValid: false,
  })

  return parsedValue ? 'us_gallon' : 'liter'
}

export type FuelUnitSetting = ReturnType<typeof getFuelUnitSetting>

export const getTwoFactorAuthenticationSetting = (state: AppState) => {
  const { twoFactorAuthentication } = getSettings(state)

  return parseBooleanUserSetting(twoFactorAuthentication, {
    // If the DB does not have a valid boolean value, we default to true. 2FA should be available by default and opt-out if needed
    defaultValueWhenNotValid: true,
  })
}

export const getCarpoolSetting = (state: AppState) => {
  const { carpool } = getSettings(state)

  return parseBooleanUserSetting(carpool, {
    defaultValueWhenNotValid: false,
  })
}

export const getShowTripsFuelConsumptionMetaDataSetting = (state: AppState) => {
  const { showTripsFuelConsumptionMetaData } = getSettings(state)

  return parseBooleanUserSetting(showTripsFuelConsumptionMetaData, {
    // Only a few countries want this off at the moment. So we enable by default
    defaultValueWhenNotValid: true,
  })
}

export const getShowAverageFuelConsumption = (state: AppState) => {
  const { showAverageFuelConsumption } = getSettings(state)

  return parseBooleanUserSetting(showAverageFuelConsumption, {
    defaultValueWhenNotValid: false,
  })
}

export const getShowMapLivePositionButton = (state: AppState) => {
  const { showMapLivePositionButton } = getSettings(state)

  return parseBooleanUserSetting(showMapLivePositionButton, {
    defaultValueWhenNotValid: false,
  })
}

export const getMaxTripLookupDaysSetting = (state: AppState): number | null => {
  const { maxTripLookupDays } = getSettings(state)
  return isNilOrEmptyString(maxTripLookupDays) ? null : Number(maxTripLookupDays)
}

export const getVehiclesActivityDatePickerUpperBaselineDateTime = ({
  privacyHideLocationsFromDay,
}: {
  privacyHideLocationsFromDay: number
}) => DateTime.now().minus({ days: Math.abs(privacyHideLocationsFromDay) })

export const getFacilityModuleNameTypeSetting = (
  state: AppState,
): 'facility' | 'unit' | 'location' | 'site' => {
  const { facilityModuleNameType } = getSettings(state)

  switch (facilityModuleNameType) {
    case 'unit': {
      return 'unit'
    }
    case 'location': {
      return 'location'
    }
    case 'site': {
      return 'site'
    }
    default: {
      return 'facility'
    }
  }
}

export const getFacilitiesTranslatorFn = createSelectorWithStrictMode(
  getFacilityModuleNameTypeSetting,
  (facilityModuleNameType) => ({
    translateFacilitiesTerm: (
      term:
        | 'global.module.facilities'
        | 'facilities.addFacility'
        | 'facilities.createFacility.success'
        | 'facilities.updateFacility.success'
        | 'facility.edit.title'
        | 'Facility'
        | 'facility.details'
        | 'controlroom.headers.facility'
        | 'facilities.deleteDialog.title'
        | 'facilities.deleteDialog.areYouSure.one'
        | 'facilities.deleteDialog.areYouSure.other'
        | 'facilities.deleteDialog.success'
        | 'All Facilities'
        | 'facilities.assignDevices.list.title'
        | 'facilities.assignDevices.uploadBox.title'
        | 'vehicleDetail.defaultFacility',
    ): string => {
      switch (term) {
        case 'global.module.facilities': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'global.module.facilities')
              .with('unit', () => 'global.module.facilities.dynamicVariant.unit')
              .with(
                'location',
                () => 'global.module.facilities.dynamicVariant.location',
              )
              .with('site', () => 'global.module.facilities.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facilities.addFacility': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.addFacility')
              .with('unit', () => 'facilities.addFacility.dynamicVariant.unit')
              .with('location', () => 'facilities.addFacility.dynamicVariant.location')
              .with('site', () => 'facilities.addFacility.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facilities.createFacility.success': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.createFacility.success')
              .with(
                'unit',
                () => 'facilities.createFacility.success.dynamicVariant.unit',
              )
              .with(
                'location',
                () => 'facilities.createFacility.success.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.createFacility.success.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'facilities.updateFacility.success': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.updateFacility.success')
              .with(
                'unit',
                () => 'facilities.updateFacility.success.dynamicVariant.unit',
              )
              .with(
                'location',
                () => 'facilities.updateFacility.success.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.updateFacility.success.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'facility.edit.title': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facility.edit.title')
              .with('unit', () => 'facility.edit.title.dynamicVariant.unit')
              .with('location', () => 'facility.edit.title.dynamicVariant.location')
              .with('site', () => 'facility.edit.title.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'Facility': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'Facility')
              .with('unit', () => 'Facility.dynamicVariant.unit')
              .with('location', () => 'Facility.dynamicVariant.location')
              .with('site', () => 'Facility.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facility.details': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facility.details')
              .with('unit', () => 'facility.details.dynamicVariant.unit')
              .with('location', () => 'facility.details.dynamicVariant.location')
              .with('site', () => 'facility.details.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'controlroom.headers.facility': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'controlroom.headers.facility')
              .with('unit', () => 'controlroom.headers.facility.dynamicVariant.unit')
              .with(
                'location',
                () => 'controlroom.headers.facility.dynamicVariant.location',
              )
              .with('site', () => 'controlroom.headers.facility.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facilities.deleteDialog.title': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.deleteDialog.title')
              .with('unit', () => 'facilities.deleteDialog.title.dynamicVariant.unit')
              .with(
                'location',
                () => 'facilities.deleteDialog.title.dynamicVariant.location',
              )
              .with('site', () => 'facilities.deleteDialog.title.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facilities.deleteDialog.areYouSure.one': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.deleteDialog.areYouSure.one')
              .with(
                'unit',
                () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.unit',
              )
              .with(
                'location',
                () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.deleteDialog.areYouSure.one.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'facilities.deleteDialog.areYouSure.other': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.deleteDialog.areYouSure.other')
              .with(
                'unit',
                () => 'facilities.deleteDialog.areYouSure.other.dynamicVariant.unit',
              )
              .with(
                'location',
                () =>
                  'facilities.deleteDialog.areYouSure.other.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.deleteDialog.areYouSure.other.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'facilities.deleteDialog.success': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.deleteDialog.success')
              .with('unit', () => 'facilities.deleteDialog.success.dynamicVariant.unit')
              .with(
                'location',
                () => 'facilities.deleteDialog.success.dynamicVariant.location',
              )
              .with('site', () => 'facilities.deleteDialog.success.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'All Facilities': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'All Facilities')
              .with('unit', () => 'All Facilities.dynamicVariant.unit')
              .with('location', () => 'All Facilities.dynamicVariant.location')
              .with('site', () => 'All Facilities.dynamicVariant.site')
              .exhaustive(),
          })
        }
        case 'facilities.assignDevices.list.title': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.assignDevices.list.title')
              .with(
                'unit',
                () => 'facilities.assignDevices.list.title.dynamicVariant.unit',
              )
              .with(
                'location',
                () => 'facilities.assignDevices.list.title.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.assignDevices.list.title.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'facilities.assignDevices.uploadBox.title': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'facilities.assignDevices.uploadBox.title')
              .with(
                'unit',
                () => 'facilities.assignDevices.uploadBox.title.dynamicVariant.unit',
              )
              .with(
                'location',
                () =>
                  'facilities.assignDevices.uploadBox.title.dynamicVariant.location',
              )
              .with(
                'site',
                () => 'facilities.assignDevices.uploadBox.title.dynamicVariant.site',
              )
              .exhaustive(),
          })
        }
        case 'vehicleDetail.defaultFacility': {
          return ctIntl.formatMessage({
            id: match(facilityModuleNameType)
              .with('facility', () => 'vehicleDetail.defaultFacility')
              .with('unit', () => 'vehicleDetail.defaultFacility.dynamicVariant.unit')
              .with(
                'location',
                () => 'vehicleDetail.defaultFacility.dynamicVariant.location',
              )
              .with('site', () => 'vehicleDetail.defaultFacility.dynamicVariant.site')
              .exhaustive(),
          })
        }
      }
    },
  }),
)

/**
 * __IMPORTANT__ Only use this if you are sure this is not sent to formatMessage.
 * Otherwise, we risk translations being used when they should not
 */
export const getFacilitiesTranslatedModuleName = (state: AppState): string =>
  getFacilitiesTranslatorFn(state).translateFacilitiesTerm('global.module.facilities')

export const getAlertCenterSetting = (state: AppState) => {
  const { controlRoom } = getSettings(state)

  return parseBooleanUserSetting(controlRoom, { defaultValueWhenNotValid: false })
}

export const getAlertCenterNewIncidentsNotificationTypeSetting = (
  state: AppState,
): 'sound' | 'off' => {
  const { controlRoomNewIncidentsNotificationType } = getSettings(state)

  switch (controlRoomNewIncidentsNotificationType) {
    case 'sound': {
      return 'sound'
    }
    default: {
      return 'off'
    }
  }
}

export const getLearningCenterMainAssetPathSetting = (
  state: AppState,
): string | null => {
  const { learningCenterMainAssetPath } = getSettings(state)

  if (isNonEmptyTrimmedString(learningCenterMainAssetPath)) {
    return learningCenterMainAssetPath
  }

  return null
}

export const getDeliveryGuideUrlSetting = createSelectorWithStrictMode(
  getSettings,
  getLocale,
  (settings, locale): string => {
    if (isNonEmptyTrimmedString(settings.deliveryGuideUrl)) {
      // If the delivery guide URL is set, we use it regardless of user selected locale
      // Useful for guides that are made for specific customers, e.g - Fleetboard Indonesia/Daimler
      return settings.deliveryGuideUrl
    }

    const settingToUse = match(locale)
      .with('pt-MZ', 'pt-PT', () => settings.deliveryGuideUrlDefaultPT)
      .with('pl', () => settings.deliveryGuideUrlDefaultPL)
      .otherwise(() => settings.deliveryGuideUrlDefaultEN)

    return isNonEmptyTrimmedString(settingToUse)
      ? settingToUse
      : 'https://fleetweb.ams3.cdn.digitaloceanspaces.com/guides/DeliveryGuide_EN.pdf'
  },
)

export const getMifleetImportsGuideNameSetting = (state: AppState): string | null => {
  const { mifleetImportsGuideName } = getSettings(state)

  if (isNonEmptyTrimmedString(mifleetImportsGuideName)) {
    return mifleetImportsGuideName
  }
  return null
}

export const getSpecialInstructionsUtilSetting = (state: AppState) => {
  const { specialInstructionsUtil } = getSettings(state)

  return parseBooleanUserSetting(specialInstructionsUtil, {
    defaultValueWhenNotValid: false,
  })
}

export const getVehicleIdToSelectOnMapAfterLogin = (state: AppState) =>
  state.user.vehicleIdToSelectOnMapAfterLogin

export const getRefreshTokenTimeoutId = (state: AppState) =>
  state.user.refreshTokenTimeoutId

export const getRefreshJwtMutation = (state: AppState) => state.user.refreshJwtMutation

export const getEnableReportPreviewUploadingSetting = (state: AppState) => {
  const { enableReportPreviewUploading } = getSettings(state)

  return parseBooleanUserSetting(enableReportPreviewUploading, {
    defaultValueWhenNotValid: false,
  })
}

export const getCountriesWebsites = (state: AppState) => state.user.countriesWebsites

export const getIdbStateForKey = createSelector(
  (state: AppState) => state.user.idbStates,
  (_: AppState, idbKey: string) => idbKey,
  (idbStates, idbKey) => idbStates[idbKey],
)

export const getUserStateFromSaga = (state: AppState) => state.user
