import { combineReducers, type Action } from '@reduxjs/toolkit'
import type { connectRouter } from 'connected-react-router'
import { persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import storageSession from 'redux-persist/lib/storage/session'
import { reducer as form } from 'redux-form'

import { startLogOut } from 'duxs/user'
import { reducer as driverDetails } from 'duxs/drivers/driver-details'
import landmarks from 'duxs/landmarks'
import loadingBar from 'duxs/loading-bar'
import map from 'duxs/map'
import openLayers from 'duxs/open-layers'
import block from 'duxs/block'
import authentication from './modules/app/authentication/slice'

// Alerts
import { reducer as costsAlerts } from 'duxs/mifleet/alerts/alerts'
import { reducer as costsActiveAlerts } from 'duxs/mifleet/alerts/active-alerts'
import trafficAlerts from 'duxs/traffic-alerts'

import { reducer as admin } from 'duxs/admin'
import dvirs from 'duxs/dvirs'
import documents from 'duxs/documents'
import { reducer as regulatory } from 'duxs/mifleet/regulatory'
import { reducer as operational } from 'duxs/mifleet/operational'
import { reducer as vehicleCosts } from 'duxs/mifleet/vehicle-costs'
import driverIdTags from 'duxs/driver-id-tags'
import confirmationModal from 'duxs/confirmation-modal'
import messageModal from 'duxs/message-modal'
import tooltip from 'duxs/tooltip'
import tripCompare from 'duxs/trip-compare'
import importer from 'duxs/importer'
import minitrackers from 'duxs/mini-tracker'
import { reducer as listData } from 'duxs/mifleet/list-data'
import { reducer as overview } from 'duxs/mifleet/overview/overview'
import { reducer as audit } from 'src/modules/admin/audit/slice'
import { editUser } from 'src/modules/admin/manage-users/EditUser/combined-slice'
import drvCostsView from 'duxs/mifleet/drivers/driver-costs'
import drvUpdateView from 'duxs/mifleet/drivers/driver-costs-form'
import driverPermits from 'duxs/mifleet/drivers/driver-permits'
import { reducer as driverStaffPositions } from 'duxs/mifleet/drivers/driver-staff-positions'
import { reducer as driverLeave } from 'duxs/mifleet/drivers/driver-leave'
import driverAssignVehicles from 'duxs/mifleet/drivers/assign-vehicles'
import mifleetDocuments from 'src/modules/mifleet/DocumentsEdit/slice'
import { reducer as suppliers } from 'duxs/mifleet/suppliers'
import { reducer as costCentres } from 'duxs/mifleet/cost-centres'
import { reducer as costCentresGroups } from 'duxs/mifleet/cost-centres-groups'
import reportProfiles from 'duxs/report-profiles'
import placeDirection from 'duxs/place-direction'
import timeline from 'duxs/timeline'
import trailers from 'duxs/trailers'
import taxes from 'duxs/taxes'
import fiscal from 'duxs/fiscal'
import messaging from 'duxs/messaging'
import { reducer as mifleetUser } from 'duxs/mifleet/user'
import { reducer as userRoles } from 'duxs/mifleet/user-roles'
import engine from 'duxs/engine'
import routes from 'duxs/routes'
import rightPanel from 'duxs/right-panel'
import survey from 'duxs/survey'
import { reducer as vehicleMapping } from 'duxs/vehicle-mapping'
// Privacy
import privacy from 'duxs/privacy'

import type { FixMeAny } from './types'
import type { Reducer } from 'react'

// Drivers Map View
import { driversMapView } from 'src/modules/map-view/DriversMapView/shared/combined-slice'

// Fullscreen Map
import fullscreenMap from 'src/modules/map-view/fullscreen-map/slice'

// Vehicle Popover
import vehiclePopover from 'src/modules/map-view/FleetMapView/LeftPanel/Vehicle/VehiclePopover/slice'

import { controlRoom } from 'src/modules/alert-center/combined-slice'
import controlRoomFilterArea from 'src/modules/alert-center/Map/FilterArea/slice'

import { createBaseRootReducer } from './root-reducer-utils'

/**
 *
 * IMPORTANT: Keep in mind that startLogOut action will invalidate persisted reducers UNLESS they are in the newState object
 */
const logOutCleanupReducer =
  (reducer: Reducer<AppState, Action>) => (state_: FixMeAny, action: Action) => {
    const state = state_ as AppState
    let newState: Partial<AppState> = { ...state }

    if (action.type === startLogOut.type) {
      /* These sub reducers will keep the state upon log out.
       If you want to do a partial cleanup in these, please listen to startLogOut action in each one of them
       ALL remaining reducers will reset their state
    */
      newState = {
        user: state.user,
        locale: state.locale,
        mifleetUser: state.mifleetUser,
        vehiclePopover: state.vehiclePopover,
        providersSync: state.providersSync,
        // While we still support connected-react-router, it's very important to keep the router state on logout to prevent weird search caching behaviour.
        // e.g - https://gitlab.cartrack.com/cartrack-base/cartrack-external/cartrack-fleet-dev/bitbucket-repos/projects/fleetapp-web/-/issues/1443
        router: state.router,
      }
    }
    return reducer(newState as FixMeAny, action)
  }

/**
 * ATTENTION DEVELOPERS !! For newly created reducers that are in TS please put it in this object.
 * If you are converting a reducer from JS to TS __please remove it from combineReducers below as well__ (to prevent duplication).
   AppState will get inferred for these reducers
 */
const getInferableReducers = (history: Parameters<typeof connectRouter>[0]) => ({
  ...createBaseRootReducer(history),
  driverDetails,
  authentication,
  admin,
  editUser,
  audit,
  userRoles,
  openLayers,
  costsAlerts,
  costsActiveAlerts,
  overview,

  driversMapView,
  fullscreenMap,
  controlRoom,
  vehicleMapping,
  controlRoomFilterArea: persistReducer(
    {
      key: 'controlRoomFilterArea',
      storage,
    },
    controlRoomFilterArea,
  ),
  mifleetUser: persistReducer(
    {
      key: 'mifleetUserSettings',
      storage: storageSession,
    },
    mifleetUser,
  ) as typeof mifleetUser,
  map: persistReducer(
    { key: 'mapSettings', storage: storageSession },
    map,
  ) as typeof map,
  tripCompare,
  timeline,
  placeDirection,
  suppliers,
  mifleetDocuments,
  dvirs,
  vehiclePopover: persistReducer(
    {
      key: 'vehiclePopoverSettings',
      storage,
    },
    vehiclePopover,
  ),
  landmarks,
  driverIdTags,
  trailers,
  minitrackers,
  importer,
  listData,
})

const createAppReducer = (history: Parameters<typeof connectRouter>[0]) =>
  combineReducers({
    ...getInferableReducers(history),
    form,
    loadingBar,
    block,
    // Alerts
    trafficAlerts,
    documents,
    regulatory,
    operational,
    vehicleCosts,
    confirmationModal,
    messageModal,
    tooltip,
    costCentres,
    costCentresGroups,
    reportProfiles,
    drvCostsView,
    drvUpdateView,
    driverPermits,
    driverStaffPositions,
    driverLeave,
    driverAssignVehicles,
    taxes,
    fiscal,
    privacy,
    messaging,
    engine,
    routes,
    rightPanel,
    survey,
  } as ReturnType<typeof getInferableReducers> & Record<string, any>)

export type AppState = ReturnType<ReturnType<typeof createAppReducer>>

export default function createRootReducer(
  history: Parameters<typeof connectRouter>[0],
) {
  return persistReducer(
    {
      key: 'root',
      storage,
      whitelist: ['routes'],
    },
    logOutCleanupReducer(createAppReducer(history)),
  )
}
