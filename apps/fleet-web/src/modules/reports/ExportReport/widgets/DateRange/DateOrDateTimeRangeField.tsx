import { useMemo, useState } from 'react'
import {
  Box,
  DateRangePicker,
  DateTimeRangePicker,
  type DateRange,
  type PickersShortcutsItem,
} from '@karoo-ui/core'
import type { Mutable } from '@mobily/ts-belt/dist/types/types'
import type { DateTime } from 'luxon'
import {
  useController,
  type Control,
  type FieldPath,
  type FieldValues,
  type PathValue,
} from 'react-hook-form'

import { ctIntl } from 'src/util-components/ctIntl'

import type { PromptValidationRange } from '../../types'
import { getValidatedDateTimeRange } from '../../util'
import { nullDateRangeFieldValue } from './utils'

type BaseFormValue = DateRange<DateTime> | null | undefined

type Props<
  TValues extends FieldValues,
  TNamePath extends FieldPath<TValues>,
  FormValue extends BaseFormValue,
> = {
  formProps: {
    name: PathValue<TValues, TNamePath> extends FormValue ? TNamePath : never
    control: Control<TValues>
  }
  shortcutItems: ReadonlyArray<PickersShortcutsItem<DateRange<DateTime>>>
  validations: PromptValidationRange
  disabled?: boolean
  limitFutureDay: boolean
  type: 'date' | 'dateTime'
}

export default function DateOrDateTimeRangeField<
  TValues extends FieldValues,
  TNamePath extends FieldPath<TValues>,
  FormValue extends BaseFormValue,
>({
  formProps,
  validations,
  shortcutItems,
  disabled,
  limitFutureDay,
  type,
}: Props<TValues, TNamePath, FormValue>) {
  const {
    field: { value: value_, onChange: onChange_ },
    fieldState,
  } = useController(formProps)
  const fieldValue = (value_ as BaseFormValue) ?? nullDateRangeFieldValue
  const fieldOnChange = onChange_ as (
    event: React.ChangeEvent<Element> | BaseFormValue,
  ) => void

  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const { start, end } = useMemo(
    () => getValidatedDateTimeRange({ validations, limitFutureDay }),
    [validations, limitFutureDay],
  )

  const { Component, dataTestId, startLocale, endLocale, format } =
    type === 'date'
      ? {
          Component: DateRangePicker,
          dataTestId: 'ReportForm-DateRange',
          startLocale: 'Start date',
          endLocale: 'End date',
          format: 'D',
        }
      : {
          Component: DateTimeRangePicker,
          dataTestId: 'ReportForm-DateTimeRange',
          startLocale: 'Start Date & Time',
          endLocale: 'End Date & Time',
          format: 'f',
        }

  return (
    <Box data-testid={dataTestId}>
      <Component
        {...(type === 'date'
          ? { minDate: start, maxDate: end }
          : { minDateTime: start, maxDateTime: end })}
        localeText={{
          start: ctIntl.formatMessage({ id: startLocale }),
          end: ctIntl.formatMessage({ id: endLocale }),
        }}
        value={fieldValue}
        onChange={(value) => fieldOnChange(value)}
        onError={(error) => {
          // NOTE: we do not handle the field error here to avoid breaking the logic of internal error handling
          if (error.includes('minDate')) {
            setErrorMessage(
              ctIntl.formatMessage(
                {
                  id: 'reports.validation.startDate.exceedMinDate',
                },
                { values: { minDate: start?.toFormat(format) } },
              ),
            )
          } else if (error.includes('maxDate')) {
            setErrorMessage(
              ctIntl.formatMessage(
                {
                  id: 'reports.validation.endDate.exceedMaxDate',
                },
                { values: { maxDate: end?.toFormat(format) } },
              ),
            )
          } else {
            setErrorMessage(null)
          }
        }}
        slotProps={{
          shortcuts: {
            items: shortcutItems as Mutable<typeof shortcutItems>,
          },
          textField: {
            error: errorMessage !== null || !!fieldState.error,
            helperText: errorMessage || fieldState.error?.message,
            required: true,
          },
          actionBar: { actions: [] },
          fieldSeparator: { children: '' },
        }}
        disabled={disabled}
      />
    </Box>
  )
}
