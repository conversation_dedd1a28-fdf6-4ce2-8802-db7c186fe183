import { useState } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  Button,
  ButtonGroup,
  CircularProgressDelayedAbsolute,
  Stack,
  Typography,
} from '@karoo-ui/core'
import { match } from 'ts-pattern'
import { z } from 'zod'

import type { FetchAvailableReportsV2Resolved } from 'api/reports'
import {
  mifleetReportReferredNameSchema,
  reportIdSchema,
  type ReportId,
} from 'api/types'
import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

import { ctToast } from 'cartrack-ui-kit'
import { useExportReportMutation, useFetchAllReportsQuery } from '../api/queries'
import ExportOneTimeForm from './ExportOneTimeForm'
import ExportRecurringForm from './ExportRecurringForm'
import { ReportBoxWithResources } from './ReportDrawer'
import type { ExportReportFormValues } from './types'

enum ExportType {
  OneTime = 'one-time',
  Recurring = 'recurring',
}

type Props = { onClose: () => void; reportId?: ReportId }

export const exportReportSearchParamsSchema = z.object({
  type: z.literal('add').or(z.literal('edit')),
  id: reportIdSchema.or(mifleetReportReferredNameSchema),
})

export default function ExportReport({ onClose, reportId }: Props) {
  const allReportsQuery = useFetchAllReportsQuery()

  const validatedParams = useValidatedSearchParams(() => exportReportSearchParamsSchema)

  return match(allReportsQuery)
    .with({ status: 'error' }, () => null)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'success' }, ({ data }) => {
      const id = validatedParams.status === 'valid' ? validatedParams.data.id : reportId

      const report = data.find(
        (
          r: FetchAvailableReportsV2Resolved[number] & {
            referredName?: string | null
          },
        ) => r.report_id === id || r.referredName === id,
      )

      if (!id || !report) {
        ctToast.fire('error', 'reports.warning.reportNotExist')
        onClose()
        return null
      }

      return (
        <ExportReportContent
          // reset the form when id change
          key={id}
          onClose={onClose}
          report={report}
        />
      )
    })
    .exhaustive()
}

function ExportReportContent({
  onClose,
  report,
}: Props & {
  report: FetchAvailableReportsV2Resolved[number]
}) {
  const companyName = useTypedSelector(getCompanyName)
  const exportReportMutation = useExportReportMutation()
  const [exportType, setExportType] = useState<
    ExportType.OneTime | ExportType.Recurring
  >(ExportType.OneTime)

  const submitForm = (report: ExportReportFormValues) => {
    exportReportMutation.mutate(
      { report },
      {
        onSuccess: () => {
          ctToast.fire('success', 'Report is processing')
          // onClose()
        },
      },
    )
  }

  return (
    <ReportBoxWithResources
      sxProps={{
        gridTemplateRows: `minmax(32px, min-content)${
          companyName === 'SCDF' ? '' : ' 36px'
        } 1fr 40px`,
      }}
    >
      <Stack
        spacing={1}
        sx={{ pr: 3 }}
      >
        <Typography
          variant="h5"
          data-testid="Report-ExportReportName"
        >
          {report.name}
        </Typography>
        {report.report_description && (
          <Typography
            data-testid="Report-ExportReportDescription"
            sx={{ opacity: 0.6 }}
          >
            {report.report_description}
          </Typography>
        )}
      </Stack>

      {companyName !== 'SCDF' && (
        <ButtonGroup>
          <Button
            variant={exportType === ExportType.OneTime ? 'contained' : 'outlined'}
            onClick={() => setExportType(ExportType.OneTime)}
            data-testid="Report-OneTimeButton"
          >
            {ctIntl.formatMessage({ id: 'reports.exportDrawer.exportType.oneTime' })}
          </Button>
          <Button
            variant={exportType === ExportType.Recurring ? 'contained' : 'outlined'}
            onClick={() => setExportType(ExportType.Recurring)}
            data-testid="Report-RecurringButton"
            disabled
          >
            {ctIntl.formatMessage({ id: 'reports.exportDrawer.exportType.recurring' })}
          </Button>
        </ButtonGroup>
      )}

      {match(exportType)
        .with(ExportType.OneTime, () => (
          <ExportOneTimeForm
            filledReport={{
              ...report,
              isMifleet: report.category?.toLowerCase() === 'mifleet',
            }}
            onClose={onClose}
            onFormSubmit={submitForm}
          />
        ))
        .with(ExportType.Recurring, () => (
          <ExportRecurringForm
            filledReport={{
              ...report,
              isMifleet: report.category?.toLowerCase() === 'mifleet',
            }}
            onClose={onClose}
            onFormSubmit={submitForm}
          />
        ))
        .exhaustive()}

      {exportReportMutation.isPending && (
        <Backdrop
          sx={{
            color: '#fff',
            zIndex: (theme) => theme.zIndex.drawer + 1,
            position: 'absolute',
            height: '100%',
            display: 'flex',
          }}
          open
        >
          <CircularProgressDelayedAbsolute
            size={60}
            thickness={4}
          />
        </Backdrop>
      )}
    </ReportBoxWithResources>
  )
}
