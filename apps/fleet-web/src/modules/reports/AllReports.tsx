// oxlint-disable no-nested-ternary
import { useMemo, useState } from 'react'
import { isEmpty } from 'lodash'
import { Box, Button, CircularProgressDelayedAbsolute, Stack } from '@karoo-ui/core'
import { useHistory, useLocation, useRouteMatch } from 'react-router'
import { match } from 'ts-pattern'

import type { FetchAvailableReportsV2Resolved } from 'api/reports'
import type { ReportId } from 'api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { useEventHandler } from 'src/hooks/useEventHandler'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import CustomReportSvg from 'assets/svg/report/CustomReport.svg'
import FavouriteReportSvg from 'assets/svg/report/FavouriteReport.svg'

import { ReportContextProvider } from './AllReportsContext'
import { useFetchAllReportsQuery } from './api/queries'
import CustomReportDrawer from './CustomReportDrawer'
import { customReportSearchParamsSchema } from './CustomReportDrawer/types'
import ExportReport from './ExportReport'
import Sidebar from './Sidebar'
import {
  StaticTabs,
  type AllReportsDispatchEvent,
  type ReportInPreview,
  type ReportType,
} from './types'
import { ImageContainer } from './util'

export default function AllReports() {
  const allReportsQuery = useFetchAllReportsQuery()

  return match(allReportsQuery)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'error' }, () => null)
    .with({ status: 'success' }, ({ data: allReports }) => (
      <AllReportsContent allReports={allReports} />
    ))
    .exhaustive()
}

const AllReportsContent = ({
  allReports,
}: {
  allReports: FetchAvailableReportsV2Resolved
}) => {
  const { path: basePath } = useRouteMatch()
  const history = useHistory()
  const [selectedTab, setSelectedTab] = useState<StaticTabs>(StaticTabs.all)
  const [selectedReport, setSelectedReport] = useState<ReportInPreview>(null)
  const [displayDefaultSection, setDisplayDefaultSection] = useState<boolean>(false)
  const location = useLocation()

  const dispatch = useEventHandler((event: AllReportsDispatchEvent) => {
    function updateTab(tab: StaticTabs) {
      setSelectedTab(tab)
      setSelectedReport(null)
      setDisplayDefaultSection(false)
    }

    function selectReport({
      favorite,
      id,
      name,
      type,
    }: {
      id: ReportId
      name: string
      favorite?: boolean
      type: ReportType
    }) {
      if (type === 'default') {
        const report = allReports.find((r) => r.report_id === id)
        if (report)
          setSelectedReport({
            id,
            name,
            description: report.report_description,
            favorite,
            type,
          })
      } else {
        setSelectedReport({
          id,
          name,
          description: null,
          favorite,
          type,
        })
      }
    }

    match(event)
      .with({ type: 'sidebar_reportlist_onItemClick' }, ({ report }) => {
        selectReport(report)
      })
      .with({ type: 'sidebar_tabs_onChange' }, ({ newValue }) => {
        updateTab(newValue)
      })
      .with({ type: 'goToAllTab' }, () => {
        updateTab(StaticTabs.all)
      })
      .with(
        { type: 'sidebar_reportlist_onMount' },
        ({ reportListInUse, reportType, searchValue }) => {
          if (!isEmpty(searchValue)) {
            // during search, no report would be selected by default
            return
          }
          if (reportListInUse.length > 0) {
            const firstReport = reportListInUse[0]
            selectReport({
              ...firstReport,
              type: reportType,
            })
          } else {
            setDisplayDefaultSection(true)
          }
        },
      )
      .with(
        { type: 'sidebar_favoriteTab_unfavorite' },
        ({ reportId, reportType, reportListInUse }) => {
          if (selectedReport?.id === reportId && reportType === 'favorite') {
            const restReports = reportListInUse.filter(
              (report) => report.id !== reportId,
            )
            if (restReports.length > 0) {
              const firstReport = restReports[0]
              selectReport({
                ...firstReport,
                type: reportType,
              })
            } else {
              setSelectedReport(null)
              setDisplayDefaultSection(true)
            }
          }
        },
      )
      .exhaustive()
  })

  const reportContextProviderValue = useMemo(
    () => ({
      dispatch,
      reports: allReports,
      selectedReportId: selectedReport?.id ?? null,
    }),
    [allReports, dispatch, selectedReport],
  )

  const showingCustomizeReport = location.pathname.includes('/customize')

  return (
    <Stack
      direction="row"
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
      }}
      data-testid="Report-AllReports"
    >
      <ReportContextProvider value={reportContextProviderValue}>
        <Sidebar selectedTab={selectedTab} />
      </ReportContextProvider>
      {showingCustomizeReport ? (
        <CustomReportDrawer onClose={() => history.push(basePath)} />
      ) : selectedReport ? (
        <ExportReport
          onClose={() => history.push(basePath)}
          reportId={selectedReport.id}
        />
      ) : (
        //It will be displayed if there are no reports in the current tab
        displayDefaultSection &&
        match(selectedTab)
          .with(StaticTabs.custom, () => (
            <DefaultRightSection
              subtitle1="report.customizeReport.introText1"
              subtitle2="report.customizeReport.introText2"
              imgSrc={CustomReportSvg}
              buttonLabel="report.customizeReport.button.createReport"
              handleOnClick={() =>
                history.push(
                  `${basePath}/customize?${buildRouteQueryStringKeepingExistingSearchParams(
                    {
                      location: history.location,
                      schema: customReportSearchParamsSchema,
                      searchParams: { type: 'add' },
                    },
                  )}`,
                )
              }
            />
          ))
          .with(StaticTabs.favorites, () => (
            <DefaultRightSection
              subtitle1="report.favourite.subtitle1"
              subtitle2="report.favourite.subtitle2"
              imgSrc={FavouriteReportSvg}
              buttonLabel="report.favourite.button.goToAllReports"
              handleOnClick={() => dispatch({ type: 'goToAllTab' })}
            />
          ))
          .otherwise(() => null)
      )}
    </Stack>
  )
}

type DefaultRightSectionProps = {
  subtitle1: string
  subtitle2: string
  imgSrc: string
  buttonLabel: string
  handleOnClick: () => void
}

const DefaultRightSection = ({
  subtitle1,
  subtitle2,
  imgSrc,
  buttonLabel,
  handleOnClick,
}: DefaultRightSectionProps) => (
  <Stack
    sx={{
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    }}
    spacing={2}
    data-testid="Reports-AllReports-Content-NoContent"
  >
    <ImageContainer {...makeSanitizedInnerHtmlProp({ dirtyHtml: imgSrc })} />
    <Box>
      <IntlTypography
        textAlign="center"
        fontWeight="bold"
        msgProps={{ id: subtitle1 }}
        data-testid="Reports-AllReports-Content-NoContent-Text1"
      />
      <IntlTypography
        textAlign="center"
        msgProps={{ id: subtitle2 }}
        data-testid="Reports-AllReports-Content-NoContent-Text2"
      />
    </Box>
    <Button
      variant="contained"
      onClick={handleOnClick}
      data-testid="Reports-AllReports-Content-NoContent-Button"
    >
      {ctIntl.formatMessage({ id: buttonLabel })}
    </Button>
  </Stack>
)
