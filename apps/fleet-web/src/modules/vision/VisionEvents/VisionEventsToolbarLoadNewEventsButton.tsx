import {
  BaseGridToolbarContainer,
  BaseGridToolbarContainerWithItems,
  Box,
  Button,
  GridToolbarSearchButtonTextField,
  Stack,
  ToolbarStandardContent,
  type ButtonProps,
} from '@karoo-ui/core'

import { ctIntl } from 'src/util-components/ctIntl'

export type VisionEventsToolbarLoadNewEventsButtonProps = {
  loadNewRowsButton: false | { props: ButtonProps }
  gridToolbarRightContent?: React.ReactNode
}

export function VisionEventsToolbarLoadNewEventsButton({
  loadNewRowsButton,
  gridToolbarRightContent,
}: VisionEventsToolbarLoadNewEventsButtonProps) {
  return (
    <BaseGridToolbarContainer sx={{ display: 'grid', alignItems: 'center' }}>
      <BaseGridToolbarContainerWithItems sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex' }}>
          <ToolbarStandardContent />
          <GridToolbarSearchButtonTextField
            GridToolbarQuickFilterProps={{ debounceMs: 500 }}
          />
        </Box>
        <Stack
          spacing={2}
          direction="row"
        >
          {gridToolbarRightContent}
        </Stack>
      </BaseGridToolbarContainerWithItems>

      {loadNewRowsButton ? (
        <Button
          variant="outlined"
          sx={{ justifySelf: 'center' }}
          {...loadNewRowsButton.props}
        >
          {ctIntl.formatMessage({ id: 'vision.events.loadNewEvents' })}
        </Button>
      ) : null}
    </BaseGridToolbarContainer>
  )
}
