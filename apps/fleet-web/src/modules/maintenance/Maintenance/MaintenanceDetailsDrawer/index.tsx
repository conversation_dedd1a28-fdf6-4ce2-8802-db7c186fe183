import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Chip,
  Divider,
  Stack,
  styled,
  Typography,
} from '@karoo-ui/core'
import CheckIcon from '@mui/icons-material/Check'
import ErrorIcon from '@mui/icons-material/Error'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import {
  MaintenanceEventStatus,
  MaintenanceEventStatusColorMapping,
  MaintenanceEventStatusTextMapping,
} from 'src/modules/carpool/utils/constants'
import DrawerBase from 'src/modules/components/unconnected/DrawerBase'

import { ctIntl, Spinner } from 'cartrack-ui-kit'
import useMaintenanceDetailsQuery from '../api/useMaintenanceDetailsQuery'

type Props = {
  onClose: () => void
  maintenanceId: string
}

const FlexBox = styled(Box)({
  display: 'flex',
  justifyContent: 'space-between',
})

const StyledAccordion = styled(Accordion)({
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
})

const StyledAccordionSummary = styled(AccordionSummary)({
  borderBottom: '1px solid rgba(155,155,155,0.4)',
  padding: 0,
  minHeight: 'auto',
  height: '34px',
})

const StyledAccordionDetails = styled(AccordionDetails)({
  padding: '16px 0',
})

const SignatureImg = styled('img')({
  width: '130px',
  position: 'relative',
  bottom: '10px',
})

const StyledImg = styled('img')({
  width: '100%',
})

function MaintenanceDetailsDrawer({ onClose, maintenanceId }: Props) {
  const maintenanceDetailsQuery = useMaintenanceDetailsQuery({
    maintenanceId: maintenanceId,
  })

  const maintenanceDetails = maintenanceDetailsQuery.data

  const mapEventStatusToIcon = (status: string) =>
    match(status)
      .with(MaintenanceEventStatus.AWAITING_REPAIR, () => ErrorIcon)
      .with(MaintenanceEventStatus.FAILED_INSPECTION, () => ErrorIcon)
      .with(MaintenanceEventStatus.PASSED_INSPECTION, () => CheckIcon)
      .with(MaintenanceEventStatus.REPAIRED, () => CheckIcon)
      .otherwise(() => null)

  return (
    <DrawerBase
      open
      onClose={onClose}
      header={maintenanceDetails?.details.maintenancePartName}
    >
      {maintenanceDetailsQuery.isPending ? (
        <Spinner absolute />
      ) : (
        <Box mt={5}>
          <Divider />
          <FlexBox
            mt={3}
            mb={3}
          >
            <Typography variant="h5">
              {maintenanceDetails?.details.vehicleRegistration}
            </Typography>

            <Chip
              label={
                maintenanceDetails?.details.status
                  ? ctIntl.formatMessage({ id: maintenanceDetails.details.status })
                  : ''
              }
              color="success"
              size="small"
            />
          </FlexBox>

          <StyledAccordion
            disableGutters
            defaultExpanded
            square
          >
            <StyledAccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography
                variant="h6"
                sx={{
                  fontSize: '18px',
                  fontWeight: 'normal',
                  color: '#666666',
                }}
              >
                {ctIntl.formatMessage({ id: 'History' })}
              </Typography>
            </StyledAccordionSummary>
            <StyledAccordionDetails>
              <Stack gap={3}>
                {maintenanceDetails?.events.map((item, index) => {
                  const StatusIcon = mapEventStatusToIcon(item.status)

                  return (
                    <Stack
                      // eslint-disable-next-line sonarjs/no-array-index-key
                      key={index}
                      gap={1}
                    >
                      <FlexBox>
                        <Typography sx={{ fontWeight: 'bold' }}>
                          {item.notes}
                        </Typography>
                        <Stack direction="row">
                          {StatusIcon && (
                            <StatusIcon
                              fontSize="small"
                              color={MaintenanceEventStatusColorMapping[item.status]}
                            />
                          )}
                          <Typography>
                            {ctIntl.formatMessage({
                              id: MaintenanceEventStatusTextMapping[item.status],
                            })}
                          </Typography>
                        </Stack>
                      </FlexBox>
                      <FlexBox>
                        <Typography>
                          {item.date
                            ? DateTime.fromSQL(item.date).toFormat('D @ T')
                            : ''}
                        </Typography>
                        <Typography>{item.odometer} km</Typography>
                      </FlexBox>

                      <Typography>{item.address.principal}</Typography>

                      <FlexBox>
                        <Typography>{item.reporterName}</Typography>
                        {item.status === MaintenanceEventStatus.PASSED_INSPECTION &&
                          maintenanceDetails?.details.driverSignature && (
                            <SignatureImg
                              src={maintenanceDetails?.details.driverSignature || ''}
                            />
                          )}
                      </FlexBox>
                    </Stack>
                  )
                })}
              </Stack>
            </StyledAccordionDetails>
          </StyledAccordion>

          {maintenanceDetails?.details.image && (
            <StyledImg src={maintenanceDetails?.details.image} />
          )}
        </Box>
      )}
    </DrawerBase>
  )
}

export default MaintenanceDetailsDrawer
