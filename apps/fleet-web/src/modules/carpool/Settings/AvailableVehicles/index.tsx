import { useMemo } from 'react'
import {
  Box,
  DataGrid,
  GridActionsCellItem,
  GridToolbarWithQuickFilter,
  LinearProgress,
  Switch,
  Tooltip,
  type GridColDef,
} from '@karoo-ui/core'
import EditIcon from '@mui/icons-material/Edit'
import { useHistory } from 'react-router'

import { getFacilitiesTranslatorFn, getSettings_UNSAFE } from 'duxs/user'
import {
  getCarpoolEditVehicle,
  getCompanyName,
} from 'src/duxs/user-sensitive-selectors'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import { useTypedSelector } from 'src/redux-hooks'
import { CellTextWithMore } from 'src/util-components/CellTextWithMore'
import { ctIntl } from 'src/util-components/ctIntl'

import {
  useAvailableVehicles,
  useUpdateVehicleAvailabilityMutation,
  useUpdateVehicleCommonPoolMutation,
  type Vehicle,
} from './api/queries'

const AvailableVehicles = () => {
  const availableVehiclesQuery = useAvailableVehicles()
  const updateVehicleAvailabilityMutation = useUpdateVehicleAvailabilityMutation()
  const updateVehicleCommonPoolMutation = useUpdateVehicleCommonPoolMutation()
  const history = useHistory()
  const companyName = useTypedSelector(getCompanyName)
  const isSCDF = companyName === 'SCDF'
  const { carpoolAppName } = useTypedSelector(getSettings_UNSAFE)
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const canEditVehicle = useTypedSelector(getCarpoolEditVehicle)

  const rows = useMemo(() => {
    if (availableVehiclesQuery.data === undefined) {
      return []
    }
    return availableVehiclesQuery.data
  }, [availableVehiclesQuery.data])

  const columns = useMemo((): Array<GridColDef<Vehicle>> => {
    const allColumns: Array<GridColDef<Vehicle>> = [
      {
        field: 'registration',
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        valueGetter: (_, row) => row.vehicle,
        flex: 1,
      },
      {
        field: 'vehicleName',
        headerName: ctIntl.formatMessage({ id: 'Vehicle Name' }),
        valueGetter: (_, row) => row.vehicleName,
        flex: 1,
      },
      {
        field: 'description',
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        valueGetter: (_, row) => row.clientVehicleDescription,
        flex: 1,
      },
      {
        field: 'description2',
        headerName: ctIntl.formatMessage({ id: 'Description 2' }),
        valueGetter: (_, row) => row.clientVehicleDescription1,
        flex: 1,
      },
      {
        field: 'description3',
        headerName: ctIntl.formatMessage({ id: 'Description 3' }),
        valueGetter: (_, row) => row.clientVehicleDescription2,
        flex: 1,
      },
      {
        field: 'manufacturer',
        headerName: ctIntl.formatMessage({ id: 'Manufacturer' }),
        valueGetter: (_, row) => row.manufacturer,
        flex: 1,
      },
      {
        field: 'model',
        headerName: ctIntl.formatMessage({ id: 'Model' }),
        valueGetter: (_, row) => row.model,
        flex: 1,
      },
      {
        field: 'year',
        headerName: ctIntl.formatMessage({ id: 'Year' }),
        valueGetter: (_, row) => row.modelYear?.toString(),
        flex: 1,
      },
      {
        field: 'colour',
        headerName: ctIntl.formatMessage({ id: 'Colour' }),
        valueGetter: (_, row) => row.colour,
        flex: 1,
      },
      {
        field: 'type',
        headerName: ctIntl.formatMessage({ id: 'Type' }),
        valueGetter: (_, row) => row.fleetVehicleType,
        flex: 1,
      },
      {
        field: 'category',
        headerName: ctIntl.formatMessage({ id: 'Category' }),
        valueGetter: (_, row) => row.bookingVehicleType,
        flex: 1,
      },
      {
        field: 'departments',
        headerName: ctIntl.formatMessage({
          id: isSCDF ? 'Vehicle Groups' : 'Departments',
        }),
        valueGetter: (_, row) => row.departments.map((d) => d.name).join(', '),
        renderCell: ({ value }) => <CellTextWithMore value={value ?? ''} />,
        flex: 1,
      } satisfies GridColDef<Vehicle, string>,
      {
        field: 'siteLocationName',
        headerName: translateFacilitiesTerm('vehicleDetail.defaultFacility'),
        valueGetter: (_, row) => row.siteLocationName,
        flex: 1,
      },
      {
        field: 'commonPool',
        headerName: ctIntl.formatMessage({ id: 'Common Pool Booking' }),
        valueGetter: (_, row) => row.commonPool,
        type: 'boolean',
        renderCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage({ id: 'Common Pool Booking' })}
            placement="top"
          >
            <Switch
              defaultChecked={row.commonPool}
              checked={row.commonPool}
              disabled={!canEditVehicle}
              onChange={(e) => {
                updateVehicleCommonPoolMutation.mutate({
                  vehicle: row.vehicle,
                  commonPool: e.target.checked,
                })
              }}
            />
          </Tooltip>
        ),
        flex: 1,
      },
      {
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        valueGetter: (_, row) => row.carpoolEnabled,
        type: 'boolean',
        renderCell: ({ row }) => (
          <Tooltip
            title={ctIntl.formatMessage(
              { id: 'availableVehicles.status.tooltip' },
              { values: { carpoolAppName } },
            )}
            placement="top"
          >
            <Switch
              disabled={!canEditVehicle}
              defaultChecked={row.carpoolEnabled}
              checked={row.carpoolEnabled}
              onChange={(e) => {
                updateVehicleAvailabilityMutation.mutate({
                  vehicle: row.vehicle,
                  carpoolEnabled: e.target.checked,
                })
              }}
            />
          </Tooltip>
        ),
        flex: 1,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        type: 'actions',
        getActions: ({ row }) => [
          <Tooltip
            key="edit"
            title={ctIntl.formatMessage({ id: 'Edit Vehicle' })}
          >
            <GridActionsCellItem
              icon={<EditIcon />}
              label={ctIntl.formatMessage({ id: 'Edit' })}
              onClick={() =>
                history.push(
                  getVehicleDetailsModalMainPath(history.location, row.id, 'SETTINGS'),
                )
              }
              color="inherit"
            />
          </Tooltip>,
        ],
        flex: 1,
      },
    ]
    return allColumns.filter((c) => c.field !== 'actions' || canEditVehicle)
  }, [
    canEditVehicle,
    carpoolAppName,
    history,
    isSCDF,
    translateFacilitiesTerm,
    updateVehicleAvailabilityMutation,
    updateVehicleCommonPoolMutation,
  ])

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        height: '100%',
        width: '100%',
        gap: 2,
      }}
    >
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        sx={{
          '& .MuiDataGrid-row': {
            cursor: 'pointer',
          },
        }}
        dataGridId="vehicleList"
        disableVirtualization
        loading={
          availableVehiclesQuery.isPending ||
          availableVehiclesQuery.isFetching ||
          updateVehicleAvailabilityMutation.isPending
        }
        autoPageSize
        slots={{
          toolbar: GridToolbarWithQuickFilter,
          loadingOverlay: LinearProgress,
        }}
        slotProps={{ toolbar: GridToolbarWithQuickFilter.createProps({}) }}
        columns={columns}
        rows={rows}
        pagination
        initialState={{
          columns: {
            columnVisibilityModel: {
              description: false,
              description2: false,
              description3: false,
            },
          },
        }}
      />
    </Box>
  )
}

export default AvailableVehicles
