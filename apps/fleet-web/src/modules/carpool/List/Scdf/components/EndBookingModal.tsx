import { useContext } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Autocomplete,
  Box,
  CircularProgress,
  DateTimePicker,
  Stack,
  TextField,
} from '@karoo-ui/core'
import { DateTime } from 'luxon'
import { Controller, useForm, useWatch } from 'react-hook-form'
import { z } from 'zod'

import {
  driverIdSchema,
  vehicleIdSchema,
  type CarpoolBookingId,
  type ClientUserId,
  type DriverId,
  type VehicleId,
} from 'api/types'
import { getAuthenticatedUser, getFacilitiesTranslatorFn } from 'duxs/user'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import useAvailableVehicleOptions from 'src/modules/carpool/components/ScdfIssuanceRequestDrawer/hooks/useAvailableVehicleOptions'
import { DriverSingleSelect } from 'src/modules/shared/DriverSingleSelect'
import UserAutocompleteRHF from 'src/modules/shared/UserAutocompleteRHF'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import CarpoolStatusChip from '../../../components/CarpoolStatusChip'
import type { BookingStatus } from '../../../utils/constants'
import { CarpoolOptionsContext } from '../../constants'
import useEndBookingMutation from '../api/useEndBookingMutation'

// Form schema
const schema = z.object({
  dropOffTime: z.string().min(1, { message: messages.required }),
  actualDropOffTime: z.string().min(1, { message: messages.required }),
  vehicleId: vehicleIdSchema,
  driverId: driverIdSchema,
  vehicleCommander: z.string().min(1, { message: messages.required }),
})

type FormData = z.infer<typeof schema>

type Props = {
  onClose: () => void
  bookingId: CarpoolBookingId
  bookingStatus: BookingStatus
  initialValues: {
    dropOffTime: string
    vehicleId: VehicleId
    driverId: DriverId
    vehicleCommanderId: ClientUserId | null
    vehicleTypeId: number | null
    locationId: number | null
    purposeid: number | null
  }
}

function EndBookingModal({ onClose, bookingId, bookingStatus, initialValues }: Props) {
  const { translateFacilitiesTerm } = useTypedSelector(getFacilitiesTranslatorFn)
  const { cuid: clientUserId } = useTypedSelector(getAuthenticatedUser)
  const { carpoolOptionsData } = useContext(CarpoolOptionsContext)
  const endBookingMutation = useEndBookingMutation()

  const {
    control,
    handleSubmit,
    setValue: setFormValue,
    formState: { isValid },
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    mode: 'all',
    defaultValues: {
      dropOffTime: initialValues?.dropOffTime || '',
      actualDropOffTime: DateTime.now().toISO(),
      vehicleId: initialValues?.vehicleId || '',
      driverId: initialValues?.driverId || '',
      vehicleCommander: initialValues?.vehicleCommanderId || '',
    },
  })

  const handleFormSubmit = handleSubmit((data) => {
    endBookingMutation.mutate(
      {
        bookingId,
        dropOffTime: new Date(data.dropOffTime),
        actualDropOffTime: new Date(data.actualDropOffTime),
        vehicleId: data.vehicleId,
        driverId: data.driverId,
        vehicleCommander: data.vehicleCommander,
        clientUserId: clientUserId ?? '',
      },
      {
        onSuccess() {
          onClose()
        },
      },
    )
  })

  const selectedVehicleId = useWatch({ control, name: 'vehicleId' })
  const selectedDriverId = useWatch({ control, name: 'driverId' })
  const facilityLabel = translateFacilitiesTerm('Facility')

  const { availableVehicleOptions, isPending } = useAvailableVehicleOptions({
    isLoadingCarpoolOptionsData: false,
    selectedVehicleId,
    selectedVehicleTypeId: initialValues.vehicleTypeId,
    carpoolOptionsData,
    selectedDriverId,
    selectedPurposeOfRequestId: initialValues.purposeid,
    selectedLocationId: initialValues.locationId,
    facilityLabel,
    showSelectedVehicle: false,
  })

  return (
    <ConfirmationModal
      title="tfms.list.endBooking.title"
      open
      onClose={onClose}
      onConfirm={handleFormSubmit}
      isLoading={endBookingMutation.isPending}
      disabledConfirmButton={!isValid}
      confirmButtonLabel="CONFIRM"
      cancelButtonLabel="CANCEL"
      titlePrefix={
        bookingStatus && <CarpoolStatusChip bookingStatusId={bookingStatus} />
      }
    >
      <Stack spacing={2}>
        <IntlTypography
          color="text.secondary"
          msgProps={{
            id: 'tfms.list.endBooking.description',
            values: { bookingId },
          }}
        />

        <Controller
          control={control}
          name="dropOffTime"
          render={({ field, fieldState }) => (
            <DateTimePicker
              label={ctIntl.formatMessage({
                id: 'tfms.list.endBooking.dropOffDateTime',
              })}
              value={field.value ? DateTime.fromISO(field.value) : null}
              onChange={(newValue) =>
                setFormValue(
                  'dropOffTime',
                  newValue && newValue.isValid ? newValue.toISO() : '',
                  { shouldValidate: true },
                )
              }
              slotProps={{
                textField: {
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  error: !!fieldState.error,
                  size: 'small',
                },
              }}
            />
          )}
        />

        <Controller
          control={control}
          name="actualDropOffTime"
          render={({ field, fieldState }) => (
            <DateTimePicker
              label={ctIntl.formatMessage({
                id: 'tfms.list.endBooking.actualDropOffDateTime',
              })}
              value={field.value ? DateTime.fromISO(field.value) : null}
              onChange={(newValue) =>
                setFormValue(
                  'actualDropOffTime',
                  newValue && newValue.isValid ? newValue.toISO() : '',
                  { shouldValidate: true },
                )
              }
              slotProps={{
                textField: {
                  helperText: ctIntl.formatMessage({
                    id: fieldState.error?.message ?? '',
                  }),
                  error: !!fieldState.error,
                  size: 'small',
                },
              }}
            />
          )}
        />

        <Controller
          control={control}
          name="vehicleId"
          render={({ field, fieldState }) => (
            <Autocomplete
              size="small"
              {...getAutocompleteVirtualizedProps({
                options: availableVehicleOptions.array,
              })}
              onChange={(_, newValue) => {
                field.onChange(newValue?.id ?? ('' as VehicleId))
              }}
              value={
                field.value
                  ? availableVehicleOptions.byId.get(field.value) ?? null
                  : null
              }
              renderInput={(params) => (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TextField
                    {...params}
                    label={ctIntl.formatMessage({ id: 'Vehicle' })}
                    helperText={ctIntl.formatMessage({
                      id: fieldState.error?.message ?? '',
                    })}
                    error={!!fieldState.error}
                    size="small"
                  />
                  {isPending && <CircularProgress size={20} />}
                </Box>
              )}
            />
          )}
        />

        <Controller
          control={control}
          name="driverId"
          render={({ field, fieldState }) => (
            <DriverSingleSelect
              size="small"
              onChange={(newValue) => {
                field.onChange(newValue ? newValue.value : ('' as DriverId))
              }}
              driverId={field.value ?? null}
              promptName={ctIntl.formatMessage({ id: 'Driver' })}
              error={fieldState.error}
            />
          )}
        />

        <UserAutocompleteRHF
          control={control}
          name="vehicleCommander"
          label={ctIntl.formatMessage({ id: 'Vehicle Commander' })}
          required
        />
      </Stack>
    </ConfirmationModal>
  )
}

export default EndBookingModal
