import { useMutation } from '@tanstack/react-query'

import type { ClientUserId, DriverId, VehicleId } from 'api/types'
import { restPatch } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'

import { ctIntl } from 'cartrack-ui-kit'
import useScdfBookingMutationInvalidation from '../../../components/ScdfIssuanceRequestDrawer/api/useScdfBookingMutationInvalidation'

export declare namespace ScdfActivateBooking {
  type Params = {
    bookingId: string
    pickUpTime: Date
    actualPickUpTime: Date
    vehicleId: VehicleId
    driverId: DriverId
    vehicleCommander: ClientUserId | null
    approver: string
  }
  type RequestPayload = {
    bookingId: number
    pickUpTime: string
    actualPickUpTime: string
    vehicleId: number
    clientDriverId: string
    vehicleCommanderClientUserId: string
    activatedClientUserId: string
  }
  type Response = {
    id: number
    message?: string
  }
}

function activateBooking(
  params: ScdfActivateBooking.Params,
): Promise<ScdfActivateBooking.Response> {
  const requestPayload: ScdfActivateBooking.RequestPayload = {
    bookingId: Number(params.bookingId),
    pickUpTime: params.pickUpTime.toISOString(),
    actualPickUpTime: params.actualPickUpTime.toISOString(),
    vehicleId: Number(params.vehicleId),
    clientDriverId: params.driverId,
    vehicleCommanderClientUserId: params.vehicleCommander ?? '',
    activatedClientUserId: params.approver,
  }

  return restPatch<ScdfActivateBooking.Response>(
    '/scdf/booking/activate',
    requestPayload,
  )
}

const useActivateBookingMutation = () => {
  const invalidateQueries = useScdfBookingMutationInvalidation()

  return useMutation({
    mutationFn: activateBooking,
    onSuccess: () => {
      enqueueSnackbarWithCloseAction(
        ctIntl.formatMessage({
          id: 'tfms.list.activateBookingSuccessMessage',
        }),
        { variant: 'success' },
      )
      invalidateQueries({ shouldInvalidateSpecificBooking: false })
    },
    onError: (error) => {
      enqueueSnackbarWithCloseAction(
        error.message ??
          ctIntl.formatMessage({
            id: 'tfms.error.activateBookingGeneralMessage',
          }),
        { variant: 'error' },
      )
    },
  })
}

export default useActivateBookingMutation
