import { useQuery } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'api/rest-api-caller'
import type { CarpoolBookingId, DriverId } from 'api/types'
import type { PromiseResolvedType } from 'src/types'
import { createQuery } from 'src/util-functions/react-query-utils'

import { BookingStatus } from '../../../utils/constants'

// Action types for different booking statuses
type BookingChanges = Array<{
  field: string
  previousValue: string | number | undefined | null
  updatedValue: string | number | undefined | null
}>

type RequestAction = {
  statusId: BookingStatus.BOOKING_STATUS_REQUESTED
}

type ApproveAction = {
  statusId: BookingStatus.BOOKING_STATUS_APPROVED
  changes: BookingChanges
  assignedDriver: string | null
  assignedVehicleCommander: string | null
}

type DeclineAction = {
  statusId: BookingStatus.BOOKING_STATUS_DECLINED
  reason: string
  remarks?: string
}

type CancelAction = {
  statusId: BookingStatus.BOOKING_STATUS_CANCELLED
  reason: string
  remarks?: string
}

type ActivateAction = {
  statusId: BookingStatus.BOOKING_STATUS_ACTIVE
  changes: BookingChanges
  actualPickupDateTime: string
}

type ActivateLateAction = {
  statusId: BookingStatus.BOOKING_STATUS_ACTIVE_LATE
  changes: BookingChanges
  actualPickupDateTime: string
}

type ReturnAction = {
  statusId: BookingStatus.BOOKING_STATUS_RETURNED
  actualDropoffDateTime: string
}

type ReturnLateAction = {
  statusId: BookingStatus.BOOKING_STATUS_RETURNED_LATE
  actualDropoffDateTime: string
  dropOffDateTime: string
}

type ForceTerminateAction = {
  statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED
  reason: string
  remarks?: string
}

type EditAction = {
  statusId: 'EDIT' // Special status for edit actions
  changes: BookingChanges
}

export type ActivityAction = {
  statusName: string
} & (
  | RequestAction
  | ApproveAction
  | DeclineAction
  | CancelAction
  | ActivateAction
  | ActivateLateAction
  | ReturnAction
  | ReturnLateAction
  | ForceTerminateAction
  | EditAction
)

export type ActivityLogEntry = {
  auTimestamp: string // ISO string
  auClientUser: { clientUserId: string | null; username: string }
  auAction: ActivityAction
}

export declare namespace FetchActivityLog {
  type OutPut = {
    bookingId: number
    bookingStatus: string
    activities: Array<ActivityLogEntry>
  }
}

// Helper function to format values for display
const formatValue = (
  value: string | null | undefined | number,
  field: string,
): string => {
  if (value === null || value === undefined || value === '') return 'null'
  if (field.includes('Date') && typeof value === 'string') {
    return DateTime.fromISO(value).toFormat('f')
  }
  if (typeof value === 'number') return value.toString()
  return value.toString()
}

// Helper function to parse and format activity log entries
const parseActivityLogEntry = (entry: ActivityLogEntry) =>
  // Use pattern matching to handle different action types
  ({
    ...entry,
    timestamp: DateTime.fromISO(entry.auTimestamp),
    action: match(entry.auAction)
      .with({ statusId: 'EDIT' }, (editAction) => ({
        ...editAction,
        formattedChanges: editAction.changes.map((change) => ({
          ...change,
          formattedText: `Update ${change.field}: ${formatValue(
            change.previousValue,
            change.field,
          )} → ${formatValue(change.updatedValue, change.field)}`,
        })),
      }))
      .with({ statusId: BookingStatus.BOOKING_STATUS_ACTIVE }, (activateAction) => ({
        ...activateAction,
        formattedPickupTime: DateTime.fromISO(
          activateAction.actualPickupDateTime,
        ).toFormat('f'),
        formattedChanges: activateAction.changes.map(
          (change) =>
            `Update ${change.field}: ${formatValue(
              change.previousValue,
              change.field,
            )} → ${formatValue(change.updatedValue, change.field)}`,
        ),
      }))
      .with(
        { statusId: BookingStatus.BOOKING_STATUS_ACTIVE_LATE },
        (activeLateAction) => ({
          ...activeLateAction,
          // NOTE: the time should be the time cron job runs
          checkingStatusTime: DateTime.fromISO(
            activeLateAction.actualPickupDateTime,
          ).toFormat('f'),
          formattedChanges: activeLateAction.changes.map(
            (change) =>
              `Update ${change.field}: ${formatValue(
                change.previousValue,
                change.field,
              )} → ${formatValue(change.updatedValue, change.field)}`,
          ),
        }),
      )
      .with({ statusId: BookingStatus.BOOKING_STATUS_RETURNED }, (returnAction) => ({
        ...returnAction,
        formattedDropoffTime: DateTime.fromISO(
          returnAction.actualDropoffDateTime,
        ).toFormat('f'),
      }))
      .with(
        { statusId: BookingStatus.BOOKING_STATUS_RETURNED_LATE },
        (returnAction) => ({
          ...returnAction,
          formattedDropoffTime: DateTime.fromISO(
            returnAction.actualDropoffDateTime,
          ).toFormat('f'),
          scheduledDropoffTime: DateTime.fromISO(returnAction.dropOffDateTime).toFormat(
            'f',
          ),
        }),
      )
      .with({ statusId: BookingStatus.BOOKING_STATUS_APPROVED }, (approveAction) => ({
        ...approveAction,
        // return the assignment details for further processing
        assignmentDetails: {
          assignedDriverId: approveAction.assignedDriver as DriverId,
          assignedVehicleCommander: approveAction.assignedVehicleCommander,
        },
        formattedChanges: approveAction.changes.map(
          (change) =>
            `Update ${change.field}: ${formatValue(
              change.previousValue,
              change.field,
            )} → ${formatValue(change.updatedValue, change.field)}`,
        ),
      }))
      .with(
        { statusId: BookingStatus.BOOKING_STATUS_DECLINED },
        { statusId: BookingStatus.BOOKING_STATUS_CANCELLED },
        { statusId: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED },
        { statusId: BookingStatus.BOOKING_STATUS_REQUESTED },
        (action) => action,
      )
      .otherwise((action) => {
        // Handle any unknown status types gracefully
        console.warn(`Unknown action status: ${entry.auAction.statusId}`)
        return action
      }),
  })

async function getActivityLog(bookingId: CarpoolBookingId) {
  // TODO: Replace with actual API call
  const rawData = await restGet<FetchActivityLog.OutPut>(
    `/scdf/booking/${bookingId}/activity-logs`,
  )

  // Parse and format the activity log entries
  return {
    ...rawData,
    activities: rawData.activities
      .map(parseActivityLogEntry)
      .sort(
        (a, b) =>
          DateTime.fromISO(b.auTimestamp).toMillis() -
          DateTime.fromISO(a.auTimestamp).toMillis(),
      ),
  }
}

export const activityLogQueryKey = (bookingId: CarpoolBookingId) =>
  ['booking/activityLog', bookingId] as const

const activityLogQuery = (bookingId: CarpoolBookingId) =>
  createQuery({
    queryKey: activityLogQueryKey(bookingId),
    queryFn: () => getActivityLog(bookingId),
    enabled: !!bookingId,
    ...makeQueryErrorHandlerWithToast(),
  })

export const useActivityLogQuery = (bookingId: CarpoolBookingId) =>
  useQuery(activityLogQuery(bookingId))

export type ActivityLogResponse = PromiseResolvedType<typeof getActivityLog>
