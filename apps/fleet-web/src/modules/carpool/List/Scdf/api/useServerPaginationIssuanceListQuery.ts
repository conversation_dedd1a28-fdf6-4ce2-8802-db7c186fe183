import type { GridLogicOperator } from '@karoo-ui/core'
import { keepPreviousData, useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'src/api/helpers'
import type { PromiseResolvedType } from 'src/types/global'
import { createQuery } from 'src/util-functions/react-query-utils'

import { secondsToMs } from 'cartrack-utils'
import type { FilterItem, SortableColumnId } from '../constants'
import { getServerPaginationIssuanceList } from './api'

export declare namespace FetchServerPaginationIssuanceList {
  type ApiInput = {
    pagination: {
      pageSize: number
      offset: number
    }
    sort: {
      field: SortableColumnId
      sort: 'asc' | 'desc'
    }
    filter: {
      items: Array<FilterItem>
      logicOperator: GridLogicOperator
      searchTextFilterValues?: Array<string>
      /**
       * - `GridLogicOperator.And`: the row must pass all the values.
       * - `GridLogicOperator.Or`: the row must pass at least one value.
       */
      searchTextFilterLogicOperator?: GridLogicOperator
    }
    statusIds: string | null
  }
  type Return = PromiseResolvedType<typeof getServerPaginationIssuanceList>

  type ApiOutput = {
    value: Array<Booking>
    metrics: Metrics
    error: string | null
    isServerError: boolean
  }

  type Booking = {
    id: number
    statusId: number
    status: string

    startDate: string
    endDate: string

    description: string
    purpose: string

    vehicleId: number
    vehicleRegistration?: string

    createdDate: string
    updatedDate?: string

    pickupTime?: string | null
    dropoffTime?: string | null

    type: string
    requestClientUserId: string
    requestedBy: string
    requestedByEmail: string
    requestedDate: string

    journeys: Array<Journey>
    locationType: number | string
    pickupLocationId: number
    pickupLocationName: string

    requestClientDriverId: string | null
    driverName?: string
    driverEmail?: string

    bookingPurposeId: number
    bookingPurposeTitle: string
    bookingPurposeDescription: string

    vehicleCategoryId: number | null
    vehicleCategoryName: string | null

    bookingReference?: string
    keyReturnTs?: string | null
    keyCollectionTs?: string | null
    accessories: Array<Accessory>
    approvedManagers: Array<ApprovedRejectedManager>
    rejectedManagers: Array<ApprovedRejectedManager>

    commanderClientUserId: string
    commanderUsername: string
    commanderEmail: string
  }

  type Journey = {
    id: number
    location: string
    startTime?: string
    endTime?: string
    order: number
  }

  type Accessory = {
    id: number
    typeId: number
    name: string
    description: string
  }

  type ApprovedRejectedManager = {
    clientUserId: string
    username: string
    email: string
  }

  type Metrics = {
    total: number
    statusMetrics: Array<StatusMetric>
  }

  type StatusMetric = {
    statusId: number
    count: number
  }
}

export type Booking = FetchServerPaginationIssuanceList.Return['bookings'][number]

const generateKey = (params: FetchServerPaginationIssuanceList.ApiInput) =>
  [...baseServerPaginationIssuanceListQueryKey, params] as const

export const baseServerPaginationIssuanceListQueryKey = [
  'carpool/serverPagination-issuanceList',
]

const issuanceListQuery = (params: FetchServerPaginationIssuanceList.ApiInput) =>
  createQuery({
    queryKey: generateKey(params),
    queryFn: () => getServerPaginationIssuanceList(params),
    staleTime: secondsToMs(20),
    // NOTE: this placehoderData is used to keep the previous data while fetching new data
    placeholderData: keepPreviousData,
    ...makeQueryErrorHandlerWithToast(),
  })

const useServerPaginationIssuanceListQuery = (
  params: FetchServerPaginationIssuanceList.ApiInput,
) => useQuery(issuanceListQuery(params))

export default useServerPaginationIssuanceListQuery
