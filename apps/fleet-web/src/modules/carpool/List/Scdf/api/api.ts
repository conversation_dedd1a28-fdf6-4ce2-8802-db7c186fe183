import { isEmpty } from 'lodash'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { restPost } from 'api/rest-api-caller'
import type { CarpoolBookingId, DriverId, VehicleId } from 'api/types'
import { BookingStatus } from 'src/modules/carpool/utils/constants'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import { ctIntl } from 'cartrack-ui-kit'
import { JOURNEY_TYPE } from '../../constants'
import type { FetchServerPaginationIssuanceList } from './useServerPaginationIssuanceListQuery'

const journeyTypeSchema = z
  .number()
  .refine(
    (val) =>
      val === JOURNEY_TYPE.SINGLE ||
      val === JOURNEY_TYPE.RETURN ||
      val === JOURNEY_TYPE.MULTIPLE,
    {
      message: 'Invalid journey type',
    },
  )

const parseJourneyType = (journeyType: number) => {
  const parsedResult = safeParseFromZodSchema(journeyTypeSchema, journeyType, {
    defaultValue: () => null,
  })
  return parsedResult === null
    ? ''
    : ctIntl.formatMessage({
        id: match(parsedResult)
          .with(JOURNEY_TYPE.SINGLE, () => 'tfms.list.singleJourney')
          .with(JOURNEY_TYPE.RETURN, () => 'tfms.list.returnJourney')
          .with(JOURNEY_TYPE.MULTIPLE, () => 'tfms.list.multiStopJourney')
          .exhaustive(),
      })
}

const parseServerPaginationIssuanceList = (
  responseData: FetchServerPaginationIssuanceList.ApiOutput,
) => ({
  bookings: responseData.value.map((i) => {
    const dropoffLocation =
      i.journeys.length > 0 ? i.journeys[i.journeys.length - 1] : null

    const journeyTypeNum = Number(i.locationType)
    const journeyType = parseJourneyType(journeyTypeNum)
    const approvedBy = i.approvedManagers.map((m) => m.email).join(';')
    const declinedBy = i.rejectedManagers.map((m) => m.email).join(';')
    const statusId = i.statusId.toString() as BookingStatus

    return {
      id: i.id.toString() as CarpoolBookingId,
      purpose: i.purpose,
      purposeDescription: i.description,
      purposeId: i.bookingPurposeId,
      status: i.status,
      statusId,
      driverName: i.driverName,
      driverEmail: i.driverEmail,
      driverId:
        i.requestClientDriverId === null ? null : (i.requestClientDriverId as DriverId),
      startDate: i.startDate,
      endDate: i.endDate,
      vehicleId: i.vehicleId === null ? null : (i.vehicleId.toString() as VehicleId),
      vehicleType: i.vehicleCategoryName,
      vehicleTypeId: i.vehicleCategoryId,
      journeyType,
      locationType: journeyTypeNum,
      vehicleCommander: i.commanderUsername,
      vehicleCommanderEmail: i.commanderEmail,
      vehicleCommanderId: i.commanderClientUserId,
      dropoffLocation: dropoffLocation ? dropoffLocation.location : null,
      dropoffLocationId: dropoffLocation ? dropoffLocation.id : null,
      pickupLocation: i.pickupLocationName,
      pickupLocationId: i.pickupLocationId,
      vehicleRegistration: i.vehicleRegistration,
      requestDate: i.requestedDate,
      keyCollectionDate: i.keyCollectionTs,
      keyReturnDate: i.keyReturnTs,
      pickupIgnitionTime: i.pickupTime,
      returnedIgnitionTime: i.dropoffTime,
      requestor: i.requestedBy,
      requestorEmail: i.requestedByEmail,
      approvalsNeeded: i.approvedManagers.length + i.rejectedManagers.length,
      approvedCount: i.approvedManagers.length,
      declinedCount: i.rejectedManagers.length,
      pendingCount: 0, // FIXME: Assuming no pending managers in the new structure
      pendingManagers: [], // Assuming no pending managers in the new structure
      approvedBy:
        statusId !== BookingStatus.BOOKING_STATUS_REQUESTED &&
        isEmpty(i.approvedManagers)
          ? '-'
          : approvedBy,
      declinedBy:
        statusId !== BookingStatus.BOOKING_STATUS_REQUESTED &&
        isEmpty(i.rejectedManagers)
          ? '-'
          : declinedBy,
    }
  }),
  metrics: responseData.metrics,
})

export const getServerPaginationIssuanceList = async (
  serverModelParam: FetchServerPaginationIssuanceList.ApiInput,
) => {
  const apiInput = {
    statusIds: serverModelParam.statusIds,
    pagination: {
      offset: serverModelParam.pagination.offset,
      pageSize: serverModelParam.pagination.pageSize,
    },
    sort: serverModelParam.sort,
    filters: {
      items: serverModelParam.filter.items,
      logicOperator: serverModelParam.filter.logicOperator,
    },
    ...(serverModelParam.filter.searchTextFilterValues?.length === 0
      ? {}
      : {
          quickSearch: {
            text: serverModelParam.filter.searchTextFilterValues,
          },
        }),
  }
  const rawResponse = await restPost<FetchServerPaginationIssuanceList.ApiOutput>(
    '/scdf/booking',
    apiInput,
    { expectSuccessValuePattern: false },
  )

  return parseServerPaginationIssuanceList(rawResponse)
}
