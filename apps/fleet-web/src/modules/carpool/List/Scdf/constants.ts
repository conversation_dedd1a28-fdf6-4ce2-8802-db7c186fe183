import { GridLogicOperator } from '@karoo-ui/core'
import * as R from 'remeda'
import { z } from 'zod'

import {
  createFilterItemSchema_Date,
  createFilterItemSchema_String,
} from 'src/shared/data-grid/server-client/server-filter-model-schemas'
import type {
  ServerFilterItem_Date,
  ServerFilterItem_String,
} from 'src/shared/data-grid/server-client/types'

import { BookingStatus, CustomTabs } from '../../utils/constants'

export const columnsIds = {
  bookingNumber: 'bookingNumber',
  vehicle: 'vehicle',
  journeyType: 'journeyType',
  driver: 'driver',
  driverEmail: 'driverEmail',
  vehicleType: 'vehicleType',
  vehicleCommanderEmail: 'vehicleCommanderEmail',
  purpose: 'purpose',
  requestorEmail: 'requestorEmail',
  requestDate: 'requestDate',
  startDate: 'startDate',
  pickUpAt: 'pickUpAt',
  pickupLocation: 'pickupLocation',
  endDate: 'endDate',
  returnedAt: 'returnedAt',
  returnedLocation: 'returnedLocation',
  status: 'status',
  approvedBy: 'approvedBy',
  declinedBy: 'declinedBy',
  actions: 'actions',
} as const

export const sortableColumnIds = [
  columnsIds.requestDate,
  columnsIds.pickUpAt,
  columnsIds.startDate,
  columnsIds.endDate,
  columnsIds.returnedAt,
] as const

export type FilterItem =
  | ServerFilterItem_String<(typeof columnsIds)['vehicle']>
  | ServerFilterItem_String<(typeof columnsIds)['driver']>
  | ServerFilterItem_String<(typeof columnsIds)['driverEmail']>
  | ServerFilterItem_String<(typeof columnsIds)['vehicleType']>
  | ServerFilterItem_String<(typeof columnsIds)['purpose']>
  | ServerFilterItem_String<(typeof columnsIds)['vehicleCommanderEmail']>
  | ServerFilterItem_String<(typeof columnsIds)['requestorEmail']>
  | ServerFilterItem_Date<(typeof columnsIds)['requestDate']>
  | ServerFilterItem_Date<(typeof columnsIds)['startDate']>

export const fetchIssuanceListFilterModelSchema = {
  items: {
    [columnsIds.vehicle]: createFilterItemSchema_String({ field: columnsIds.vehicle }),
    [columnsIds.driver]: createFilterItemSchema_String({ field: columnsIds.driver }),
    [columnsIds.driverEmail]: createFilterItemSchema_String({
      field: columnsIds.driverEmail,
    }),
    [columnsIds.vehicleType]: createFilterItemSchema_String({
      field: columnsIds.vehicleType,
    }),
    [columnsIds.purpose]: createFilterItemSchema_String({ field: columnsIds.purpose }),
    [columnsIds.requestorEmail]: createFilterItemSchema_String({
      field: columnsIds.requestorEmail,
    }),
    [columnsIds.vehicleCommanderEmail]: createFilterItemSchema_String({
      field: columnsIds.vehicleCommanderEmail,
    }),
    [columnsIds.requestDate]: createFilterItemSchema_Date({
      field: columnsIds.requestDate,
      operators: ['range'],
    })(({ create, field, operators }) =>
      create({ field, operators: z.enum(operators) }),
    ),
    [columnsIds.startDate]: createFilterItemSchema_Date({
      field: columnsIds.startDate,
      operators: ['range'],
    })(({ create, field, operators }) =>
      create({ field, operators: z.enum(operators) }),
    ),
  },
  get self() {
    const items = this.items
    return z.object({
      items: z.array(
        z.union([
          items[columnsIds.vehicle],
          items[columnsIds.driver],
          items[columnsIds.driverEmail],
          items[columnsIds.vehicleType],
          items[columnsIds.purpose],
          items[columnsIds.vehicleCommanderEmail],
          items[columnsIds.requestorEmail],
          items[columnsIds.requestDate],
          items[columnsIds.startDate],
          // TODO: add more fields
        ]),
      ),
      logicOperator: z.nativeEnum(GridLogicOperator),
      quickFilterValues: z.array(z.string()),
    })
  },
}

export const filterableColumnIds = R.values(
  fetchIssuanceListFilterModelSchema.items,
).map((item) => item.shape.field.value)

export type FilterableColumnId = (typeof filterableColumnIds)[number]

export type FetchIssuanceListFilterModelSchemaSelf = z.infer<
  typeof fetchIssuanceListFilterModelSchema.self
>

export type SortableColumnId = (typeof sortableColumnIds)[number]

export type Metric =
  | 'ACTIVE'
  | 'ACTIVE_ALMOST_LATE'
  | 'ACTIVE_LATE'
  | 'APPROVED'
  | 'CANCELLED'
  | 'DECLINED'
  | 'EXPIRING_APPROVAL'
  | 'FREE'
  | 'REQUESTED'
  | 'RETURNED'
  | 'RETURNED_LATE'
  | 'TOTAL'
  | 'FORCE_TERMINATED'

export const metricConfig: Array<{
  key: Metric
  label: string
  tab?: CustomTabs
  id?: BookingStatus
}> = [
  { key: 'TOTAL', label: 'Total' },
  {
    key: 'ACTIVE',
    label: 'Active',
    tab: CustomTabs.ACTIVE,
    id: BookingStatus.BOOKING_STATUS_ACTIVE,
  },
  {
    key: 'ACTIVE_LATE',
    label: 'Active Late',
    tab: CustomTabs.ACTIVE_LATE,
    id: BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
  },
  {
    key: 'REQUESTED',
    label: 'Requested',
    tab: CustomTabs.REQUESTED,
    id: BookingStatus.BOOKING_STATUS_REQUESTED,
  },
  {
    key: 'APPROVED',
    label: 'global.approved',
    tab: CustomTabs.APPROVED,
    id: BookingStatus.BOOKING_STATUS_APPROVED,
  },
  {
    key: 'DECLINED',
    label: 'scdf.status.declined',
    tab: CustomTabs.DECLINED,
    id: BookingStatus.BOOKING_STATUS_DECLINED,
  },
  {
    key: 'CANCELLED',
    label: 'Cancelled',
    tab: CustomTabs.CANCELLED,
    id: BookingStatus.BOOKING_STATUS_CANCELLED,
  },
  {
    key: 'FREE',
    label: 'Free',
    tab: CustomTabs.FREE,
    id: BookingStatus.BOOKING_STATUS_FREE,
  },
  {
    key: 'RETURNED',
    label: 'Completed',
    tab: CustomTabs.RETURNED,
    id: BookingStatus.BOOKING_STATUS_RETURNED,
  },
  {
    key: 'RETURNED_LATE',
    label: 'Completed Late',
    tab: CustomTabs.RETURNED_LATE,
    id: BookingStatus.BOOKING_STATUS_RETURNED_LATE,
  },
  {
    key: 'FORCE_TERMINATED',
    label: 'Force Terminated',
    tab: CustomTabs.FORCE_TERMINATED,
    id: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
  },
] as const
