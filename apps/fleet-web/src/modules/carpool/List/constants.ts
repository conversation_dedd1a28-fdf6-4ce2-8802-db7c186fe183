import { createContext } from 'react'
import type { ValueOf } from 'type-fest'

import type { FetchCarpoolOptionsParsedData } from '../queries/useCarpoolOptionsQuery'
import { BookingStatus, CustomTabs } from '../utils/constants'

export const LIST_TABS = {
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'inProgress',
  TODAY: 'today',
  HISTORY: 'history',
  CUSTOM: 'custom',
} as const

export const CarpoolOptionsContext = createContext({
  carpoolOptionsData: {} as FetchCarpoolOptionsParsedData,
})

export const TAB_OPTIONS: Array<{
  label: string
  value: ValueOf<typeof LIST_TABS>
}> = [
  {
    label: 'Scheduled',
    value: LIST_TABS.SCHEDULED,
  },
  {
    label: 'In Progress',
    value: LIST_TABS.IN_PROGRESS,
  },
  {
    label: 'History',
    value: LIST_TABS.HISTORY,
  },
]

export const getSCDFTabOptions = (
  appName: string,
): Array<{
  label: string
  value: ValueOf<typeof LIST_TABS>
}> => [
  {
    label: appName + ' In Progress',
    value: LIST_TABS.IN_PROGRESS,
  },
  {
    label: "Today's " + appName,
    value: LIST_TABS.TODAY,
  },
  {
    label: 'Scheduled',
    value: LIST_TABS.SCHEDULED,
  },
  {
    label: 'History',
    value: LIST_TABS.HISTORY,
  },
]

export const customTabAndStatusMapping = {
  [CustomTabs.APPROVED]: BookingStatus.BOOKING_STATUS_APPROVED,
  [CustomTabs.REQUESTED]: BookingStatus.BOOKING_STATUS_REQUESTED,
  [CustomTabs.DECLINED]: BookingStatus.BOOKING_STATUS_DECLINED,
  [CustomTabs.CANCELLED]: BookingStatus.BOOKING_STATUS_CANCELLED,
  [CustomTabs.ACTIVE]: BookingStatus.BOOKING_STATUS_ACTIVE,
  [CustomTabs.ACTIVE_LATE]: BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
  [CustomTabs.FREE]: BookingStatus.BOOKING_STATUS_FREE,
  [CustomTabs.RETURNED]: BookingStatus.BOOKING_STATUS_RETURNED,
  [CustomTabs.RETURNED_LATE]: BookingStatus.BOOKING_STATUS_RETURNED_LATE,
  [CustomTabs.FORCE_TERMINATED]: BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
}

export const LAYER_VISIBILITY = {
  alerts: false,
  bicycle: false,
  geofenceLabels: false,
  geofences: false,
  harshEvents: false,
  harshEventsSpeeding: false,
  landmarks: false,
  livePositionClusters: false,
  livePositionLabels: false,
  livePositionShowWhileAVehicleIsSelected: false,
  livePositions: false,
  maps: false,
  pointsOfInterestLabels: false,
  showTripLines: true,
  systemGeofences: false,
  traffic: false,
  transit: false,
  userGeofences: false,
}

export const JOURNEY_TYPE = {
  SINGLE: 0,
  RETURN: 1,
  MULTIPLE: 2,
} as const
