import { useMemo } from 'react'
import { isEmpty } from 'lodash'
import { <PERSON>ack, Tooltip, Typography } from '@karoo-ui/core'
import moment, { type MomentInput } from 'moment'
import { connect } from 'react-redux'

import { getServerTime } from 'duxs/user'
import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { PANEL_DEFAULT_WIDTH } from 'src/components/Panel'
import { TimelineItem } from 'src/modules/map-view/utils'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'

import { ctIntl, OldTimelineBar } from 'cartrack-ui-kit'
import useDriversFittedToSize from '../hooks/useDriversFittedToSize'
import {
  EventStatusColorMapping,
  SCDFBookingDetailsEventStatusTextMapping,
  SPFBookingDetailsEventStatusTextMapping,
  type BookingDetailsEventStatus,
} from '../utils/constants'

export type Driver = {
  driver_name: string
  driver_id: string
}

type Props = {
  date?: string
  events:
    | Array<{
        event: BookingDetailsEventStatus
        pctLeft: number
        pctWidth: number
      }>
    | []
  drivers: Array<Driver> | []
} & ReduxProps

const X_PADDING_X2 = 64
const MAXIMUM_AVAILABLE_WIDTH = Number.parseInt(PANEL_DEFAULT_WIDTH, 10) - X_PADDING_X2

const ReTimelineBarBlock = ({ events, drivers, date, getServerTime }: Props) => {
  const companyName = useTypedSelector(getCompanyName)
  const isScdf = companyName === 'SCDF'

  const BookingDetailsEventStatusTextMapping = isScdf
    ? SCDFBookingDetailsEventStatusTextMapping
    : SPFBookingDetailsEventStatusTextMapping
  const { fittedDrivers, unfittedDrivers } = useDriversFittedToSize(
    drivers,
    MAXIMUM_AVAILABLE_WIDTH,
  )

  const renderDateSection = () => {
    if (!date) {
      return null
    }

    let dayLabel

    const day = getServerTime(date).format('YYYY-MM-DD')
    const todayDate = getServerTime().format('YYYY-MM-DD')
    const yesterdayDate = getServerTime().subtract(1, 'days').format('YYYY-MM-DD')

    if (day === todayDate) {
      dayLabel = ctIntl.formatMessage({ id: `Today` })
    } else if (day === yesterdayDate) {
      dayLabel = ctIntl.formatMessage({ id: `Yesterday` })
    } else {
      dayLabel = ctIntl.formatMessage({
        id: moment(day).format('dddd'),
      })
    }

    return dayLabel
  }

  const formattedEvents = useMemo(
    () =>
      events.map((e) => ({
        ...e,
        backgroundColor: EventStatusColorMapping[e.event],
        event: BookingDetailsEventStatusTextMapping[e.event],
      })),
    [BookingDetailsEventStatusTextMapping, events],
  )

  const renderDriversSection = () => {
    if (!drivers || isEmpty(drivers)) {
      return null
    }
    return (
      <Stack
        gap={1}
        mt={1}
      >
        <Typography variant="caption">
          {`${ctIntl.formatMessage({ id: 'Booked drivers:' })} ${drivers.length}`}
        </Typography>
        <Stack
          direction="row"
          justifyContent="space-between"
        >
          <Stack
            direction="row"
            gap={0.7}
          >
            {fittedDrivers.map((driver, idx) => (
              <Typography
                key={driver.driver_id}
                variant="body2"
              >
                {`${driver.driver_name}${fittedDrivers.length - 1 === idx ? '' : ', '}`}
              </Typography>
            ))}
          </Stack>
          {unfittedDrivers.length > 0 && (
            <Tooltip
              title={
                <>
                  {unfittedDrivers.map((driver) => (
                    <Typography
                      variant="body2"
                      key={driver.driver_id}
                    >
                      {driver.driver_name}
                    </Typography>
                  ))}
                </>
              }
              placement="right"
            >
              <Typography variant="body2">{`+${unfittedDrivers.length}`}</Typography>
            </Tooltip>
          )}
        </Stack>
      </Stack>
    )
  }

  return (
    <TimelineItem isSelected={false}>
      <>
        <Typography
          sx={{ mb: 1 }}
          variant="subtitle2"
        >
          {renderDateSection()}
        </Typography>
        <OldTimelineBar
          eventTooltip
          events={formattedEvents}
        />
        {renderDriversSection()}
      </>
    </TimelineItem>
  )
}

const mapStateToProps = (state: AppState) => ({
  getServerTime: (dateTime?: MomentInput) => getServerTime(state, dateTime),
})

type ReduxProps = ReturnType<typeof mapStateToProps>

export default connect(mapStateToProps)(ReTimelineBarBlock)
