import { useMemo } from 'react'
import { isEmpty } from 'lodash'
import { Tooltip } from '@karoo-ui/core'
import moment, { type MomentInput } from 'moment'
import { connect } from 'react-redux'
import styled from 'styled-components'

import { getServerTime } from 'duxs/user'
import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'

import { ctIntl, OldTimelineBar } from 'cartrack-ui-kit'
import useDriversFittedToSize from '../hooks/useDriversFittedToSize'
import {
  EventStatusColorMapping,
  SCDFBookingDetailsEventStatusTextMapping,
  SPFBookingDetailsEventStatusTextMapping,
  type BookingDetailsEventStatus,
} from '../utils/constants'

export type Driver = {
  driver_name: string
  driver_id: string
}

type Props = {
  date?: string
  events:
    | Array<{
        event: BookingDetailsEventStatus
        pctLeft: number
        pctWidth: number
      }>
    | []
  drivers: Array<Driver> | []
} & ReduxProps

const DETAILS_PANEL_MAXIMUM_WIDTH = 300
const MAXIMUM_AVAILABLE_WIDTH = DETAILS_PANEL_MAXIMUM_WIDTH - (28 + 40)

const TimelineBarBlock = ({ events, drivers, date, getServerTime }: Props) => {
  const companyName = useTypedSelector(getCompanyName)
  const isScdf = companyName === 'SCDF'

  const BookingDetailsEventStatusTextMapping = isScdf
    ? SCDFBookingDetailsEventStatusTextMapping
    : SPFBookingDetailsEventStatusTextMapping

  const { fittedDrivers, unfittedDrivers } = useDriversFittedToSize(
    drivers,
    MAXIMUM_AVAILABLE_WIDTH,
  )

  const renderDateSection = () => {
    if (!date) {
      return null
    }

    let dayLabel

    const day = getServerTime(date).format('YYYY-MM-DD')
    const todayDate = getServerTime().format('YYYY-MM-DD')
    const yesterdayDate = getServerTime().subtract(1, 'days').format('YYYY-MM-DD')

    if (day === todayDate) {
      dayLabel = ctIntl.formatMessage({ id: `Today` })
    } else if (day === yesterdayDate) {
      dayLabel = ctIntl.formatMessage({ id: `Yesterday` })
    } else {
      dayLabel = ctIntl.formatMessage({
        id: moment(day).format('dddd'),
      })
    }

    return <StyledDateDDDD>{dayLabel}</StyledDateDDDD>
  }

  const formattedEvents = useMemo(
    () =>
      events.map((e) => ({
        ...e,
        backgroundColor: EventStatusColorMapping[e.event],
        event: BookingDetailsEventStatusTextMapping[e.event],
      })),
    [BookingDetailsEventStatusTextMapping, events],
  )

  const renderDriversSection = () => {
    if (!drivers || isEmpty(drivers)) {
      return null
    }

    return (
      <StyledDriversContainer>
        <StyledDriversCount>{`${ctIntl.formatMessage({ id: 'Drivers' })}: ${
          drivers.length
        }`}</StyledDriversCount>
        <StyledDriverNamesContainer>
          <StyledDriverNamesWordWrapper>
            {fittedDrivers.map((driver, index) => (
              <StyledSingleDriver
                leftMargin={index === 0 ? 0 : '6px'}
                key={driver.driver_id}
              >
                {driver.driver_name}
              </StyledSingleDriver>
            ))}
          </StyledDriverNamesWordWrapper>
          {unfittedDrivers.length > 0 && (
            <Tooltip
              title={
                <>
                  {unfittedDrivers.map((driver) => (
                    <StyledHoverDriverName key={driver.driver_id}>
                      {driver.driver_name}
                    </StyledHoverDriverName>
                  ))}
                </>
              }
              placement="right"
            >
              <StyledDriverBadge>{`+${unfittedDrivers.length}`}</StyledDriverBadge>
            </Tooltip>
          )}
        </StyledDriverNamesContainer>
      </StyledDriversContainer>
    )
  }

  return (
    <StyledContainer>
      {renderDateSection()}
      <OldTimelineBar
        eventTooltip
        events={formattedEvents}
      />
      {renderDriversSection()}
    </StyledContainer>
  )
}

const mapStateToProps = (state: AppState) => ({
  getServerTime: (dateTime?: MomentInput) => getServerTime(state, dateTime),
})

type ReduxProps = ReturnType<typeof mapStateToProps>

export default connect(mapStateToProps)(TimelineBarBlock)

const StyledContainer = styled.div`
  border-top: 1px solid lightgrey;
  display: flex;
  flex-direction: column;
  padding: 14px;
`

const StyledDateDDDD = styled.span`
  font-weight: bold;
  color: #333333;
  font-size: 16px;
  margin-bottom: 8px;
`

const StyledDriversContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-top: 8px;
`
const StyledDriversCount = styled.p`
  margin: 0;
  font-size: 12px;
  font-weight: bold;
  color: #5c5c5c;
`
const StyledDriverNamesContainer = styled.div`
  display: flex;
  justify-content: space-between;
`

const StyledDriverNamesWordWrapper = styled.div`
  display: flex;
  align-items: center;
`

const StyledSingleDriver = styled.p<{ leftMargin: string | number }>`
  border-right: 2px solid lightgrey;
  padding-right: 20px;
  padding-left: ${({ leftMargin }) => leftMargin};
  margin: 0;
  font-size: 12px;
  color: #5c5c5c;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  text-align: center;
  max-height: 15px;
`
const StyledDriverBadge = styled.div`
  padding: 2px;
  background-color: #0d3c61;
  color: white;
  border: 1px solid transparent;
  border-radius: 12px;
  font-size: 12px;
  min-width: 25px;
  display: flex;
  justify-content: center;

  &:hover {
    cursor: default;
  }
`
const StyledHoverDriverName = styled.p`
  margin: 0;
  font-size: 12px;
`
