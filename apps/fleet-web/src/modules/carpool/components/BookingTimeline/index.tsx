import {
  Box,
  Chip,
  Timeline,
  TimelineConnector,
  timelineConnectorClasses,
  TimelineContent,
  timelineContentClasses,
  TimelineItem,
  timelineItemClasses,
  TimelineSeparator,
  timelineSeparatorClasses,
  Typography,
} from '@karoo-ui/core'
import CropIcon from '@mui/icons-material/CropFree'
import WarningIcon from '@mui/icons-material/Warning'
import { DateTime } from 'luxon'
import { match, P } from 'ts-pattern'

import { getAuthenticatedUser, getSpecialLicensesDynamicFeatureName } from 'duxs/user'
import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import { ctIntl } from 'cartrack-ui-kit'
import type { FetchBookingDetails } from '../../List/Spf/api/useBookingDetailsQuery'
import {
  BookingDetailsEventStatus,
  EventStatusColorMapping,
  EventStatusTextColorMapping,
  SCDFBookingDetailsEventStatusTextMapping,
  SPFBookingDetailsEventStatusTextMapping,
} from '../../utils/constants'

type Props = {
  bookingDetails: FetchBookingDetails.Return | undefined
}

function BookingTimeline({ bookingDetails }: Props) {
  const user = useTypedSelector(getAuthenticatedUser)
  const specialLicensesLabel = useTypedSelector(getSpecialLicensesDynamicFeatureName)
  const companyName = useTypedSelector(getCompanyName)
  const isScdf = companyName === 'SCDF'

  const BookingDetailsEventStatusTextMapping = isScdf
    ? SCDFBookingDetailsEventStatusTextMapping
    : SPFBookingDetailsEventStatusTextMapping

  const mapEventStatusToDisplayedChip = (status: BookingDetailsEventStatus) =>
    match(status)
      .with(
        BookingDetailsEventStatus.REQUESTED,
        BookingDetailsEventStatus.ACTIVE,
        BookingDetailsEventStatus.ACTIVE_ALMOST_LATE,
        BookingDetailsEventStatus.ACTIVE_LATE,
        BookingDetailsEventStatus.APPROVED,
        BookingDetailsEventStatus.CANCELLED,
        BookingDetailsEventStatus.DECLINED,
        BookingDetailsEventStatus.KEY_COLLECTED,
        BookingDetailsEventStatus.KEY_RETURNED,
        BookingDetailsEventStatus.RETURNED,
        BookingDetailsEventStatus.RETURNED_LATE,
        BookingDetailsEventStatus.FORCE_TERMINATED,
        () => (
          <Chip
            size="small"
            variant="filled"
            label={BookingDetailsEventStatusTextMapping[status]}
            sx={{
              backgroundColor: EventStatusColorMapping[status],
              color: EventStatusTextColorMapping[status],
            }}
            icon={
              [
                BookingDetailsEventStatus.RETURNED_LATE,
                BookingDetailsEventStatus.ACTIVE_LATE,
                BookingDetailsEventStatus.FORCE_TERMINATED,
              ].includes(status) ? (
                <WarningIcon
                  sx={{
                    ml: 2,
                    height: '18px',
                    color: '#FF9800 !important',
                  }}
                />
              ) : undefined
            }
          />
        ),
      )
      .with(
        BookingDetailsEventStatus.ENTERED_DROP_OFF,
        BookingDetailsEventStatus.LEFT_PICK_UP,
        () => <CropIcon />,
      )
      .otherwise(() => null)

  const mapEventStatusToTimelineItemTitle = (status: BookingDetailsEventStatus) =>
    match(status)
      .with(BookingDetailsEventStatus.ACTIVE, () => 'carpool.list.timeline.active')
      .with(
        BookingDetailsEventStatus.ACTIVE_ALMOST_LATE,
        () => 'carpool.list.timeline.activeAlmostLate',
      )
      .with(
        BookingDetailsEventStatus.ACTIVE_LATE,
        () => 'carpool.list.timeline.activeLate',
      )
      .with(BookingDetailsEventStatus.APPROVED, () => 'carpool.list.timeline.approved')
      .with(
        BookingDetailsEventStatus.CANCELLED,
        () => 'carpool.list.timeline.cancelled',
      )
      .with(BookingDetailsEventStatus.DECLINED, () => 'carpool.list.timeline.declined')
      .with(
        BookingDetailsEventStatus.ENTERED_DROP_OFF,
        () => 'carpool.list.timeline.enterDropoff',
      )
      .with(
        BookingDetailsEventStatus.LEFT_PICK_UP,
        () => 'carpool.list.timeline.leftPickup',
      )
      .with(
        BookingDetailsEventStatus.REQUESTED,
        () => 'carpool.list.timeline.requested',
      )
      .with(BookingDetailsEventStatus.RETURNED, () => 'carpool.list.timeline.returned')
      .with(
        BookingDetailsEventStatus.RETURNED_LATE,
        () => 'carpool.list.timeline.returnedLate',
      )
      .with(
        BookingDetailsEventStatus.KEY_COLLECTED,
        () => 'carpool.list.timeline.keyCollected',
      )
      .with(
        BookingDetailsEventStatus.KEY_RETURNED,
        () => 'carpool.list.timeline.keyReturned',
      )
      .with(
        BookingDetailsEventStatus.FORCE_TERMINATED,
        () => 'carpool.list.timeline.tripTerminated',
      )
      .otherwise(() => '')

  const renderEventDetail = (label: string, content: string | null) => {
    if (!content) return null

    const eventDetailHtml = `<strong>${ctIntl.formatMessage({
      id: label,
    })}:</strong> ${content}`

    return (
      <Box
        sx={{ fontSize: '14px' }}
        {...makeSanitizedInnerHtmlProp({ dirtyHtml: eventDetailHtml })}
      />
    )
  }

  // get the name of the person who performs certain actions
  const getPersonName = (
    event: FetchBookingDetails.Return['events'][number],
  ): string => {
    if (
      event.status === BookingDetailsEventStatus.REQUESTED ||
      event.status === BookingDetailsEventStatus.CANCELLED
    ) {
      if (event.client_driver_name) {
        return event.client_driver_name
      } else if (event.client_user_name) {
        return event.client_user_name
      } else {
        return user.username
      }
    } else if (
      event.status === BookingDetailsEventStatus.DECLINED ||
      event.status === BookingDetailsEventStatus.APPROVED
    ) {
      if (event.client_user_name) {
        return event.client_user_name
      } else {
        return user.username
      }
    } else if (event.status === BookingDetailsEventStatus.FORCE_TERMINATED) {
      if (event.client_user_name) {
        return event.client_user_name
      } else {
        return user.username
      }
    }

    return 'N/A'
  }

  return bookingDetails ? (
    <Timeline
      position="right"
      sx={{
        [`& .${timelineItemClasses.root}:before`]: {
          flex: 0,
          padding: 0,
        },
        [`& .${timelineConnectorClasses.root}`]: {
          margin: '20px 0',
        },
        [`& .${timelineContentClasses.root}`]: {
          padding: '0 16px',
        },
        [`& .${timelineSeparatorClasses.root}`]: {
          pt: '6px',
          flex: '0.5',
        },
        padding: 0,
      }}
    >
      {bookingDetails.events.map((event, index) => (
        <TimelineItem key={event.date}>
          <TimelineSeparator>
            {mapEventStatusToDisplayedChip(event.status)}
            {index < bookingDetails.events.length - 1 ? <TimelineConnector /> : null}
          </TimelineSeparator>
          <TimelineContent>
            <Typography variant="h6">
              {ctIntl.formatMessage({
                id: mapEventStatusToTimelineItemTitle(event.status),
              })}
            </Typography>
            <Typography
              sx={{ opacity: 0.6 }}
              mb={0.5}
            >
              {event.date ? DateTime.fromSQL(event.date).toFormat('D t') : ''}
            </Typography>
            <Box>
              {match(event)
                .with(
                  { status: BookingDetailsEventStatus.REQUESTED },
                  (matchedEvent) => (
                    <>
                      {renderEventDetail(
                        'carpool.bookingDetails.requestor',
                        bookingDetails.requestor || getPersonName(matchedEvent),
                      )}
                      {renderEventDetail(
                        'carpool.list.addEditBooking.purposeOfRequest',
                        bookingDetails.bookingPurpose || 'N/A',
                      )}
                    </>
                  ),
                )
                .with(
                  { status: BookingDetailsEventStatus.APPROVED },
                  (matchedEvent) => (
                    <>
                      {renderEventDetail(
                        'carpool.bookingDetails.approver',
                        getPersonName(matchedEvent),
                      )}
                      {renderEventDetail(
                        'Vehicle',
                        bookingDetails.vehicleRegistration || 'N/A',
                      )}
                      {renderEventDetail('Type', bookingDetails.vehicleType || 'N/A')}
                    </>
                  ),
                )
                .with(
                  {
                    status: P.union(
                      BookingDetailsEventStatus.ACTIVE,
                      BookingDetailsEventStatus.ACTIVE_ALMOST_LATE,
                      BookingDetailsEventStatus.ACTIVE_LATE,
                    ),
                  },
                  () => (
                    <>
                      {renderEventDetail(
                        'Pick up',
                        bookingDetails.pickupLocation || 'N/A',
                      )}
                    </>
                  ),
                )
                .with(
                  {
                    status: P.union(
                      BookingDetailsEventStatus.LEFT_PICK_UP,
                      BookingDetailsEventStatus.ENTERED_DROP_OFF,
                      BookingDetailsEventStatus.KEY_COLLECTED,
                      BookingDetailsEventStatus.KEY_RETURNED,
                    ),
                  },
                  (matchedEvent) => (
                    <>
                      {renderEventDetail('Driver', bookingDetails.driverName || 'N/A')}
                      {renderEventDetail('License', matchedEvent.license || 'N/A')}
                      {renderEventDetail(
                        specialLicensesLabel,
                        matchedEvent.special_license
                          ?.map((l) => l.license_name)
                          .join(', ') || 'N/A',
                      )}
                    </>
                  ),
                )
                .with(
                  {
                    status: P.union(
                      BookingDetailsEventStatus.RETURNED,
                      BookingDetailsEventStatus.RETURNED_LATE,
                    ),
                  },
                  () => (
                    <>
                      {renderEventDetail(
                        'carpool.list.addEditBooking.dropoff',
                        bookingDetails.dropoffLocation || 'N/A',
                      )}
                    </>
                  ),
                )
                .with(
                  { status: BookingDetailsEventStatus.DECLINED },
                  (matchedEvent) => (
                    <>{renderEventDetail('Decline', getPersonName(matchedEvent))}</>
                  ),
                )
                .with(
                  { status: BookingDetailsEventStatus.CANCELLED },
                  (matchedEvent) => (
                    <>
                      {renderEventDetail('Cancelled', getPersonName(matchedEvent))}
                      {renderEventDetail('Reason', matchedEvent.booking_cancel_reason)}
                      {renderEventDetail('Notes', matchedEvent.booking_cancel_notes)}
                    </>
                  ),
                )
                .with(
                  { status: BookingDetailsEventStatus.FORCE_TERMINATED },
                  (matchedEvent) => (
                    <>
                      {renderEventDetail(
                        'carpool.bookingDetails.terminatedBy',
                        getPersonName(matchedEvent),
                      )}
                    </>
                  ),
                )
                .otherwise(() => '')}
            </Box>
          </TimelineContent>
        </TimelineItem>
      ))}
    </Timeline>
  ) : null
}

export default BookingTimeline
