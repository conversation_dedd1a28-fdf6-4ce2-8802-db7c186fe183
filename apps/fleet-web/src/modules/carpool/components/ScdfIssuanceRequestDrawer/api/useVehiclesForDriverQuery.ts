import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'api/rest-api-caller'
import type { CompanyDepartmentId, DriverId, VehicleId } from 'api/types'
import { createQuery } from 'src/util-functions/react-query-utils'

import { isNilOrEmptyString } from 'cartrack-utils'

export declare namespace FetchVehiclesForDriver {
  type License = {
    licenseTypeId: number
    licenseName: string
  }
  type Vehicle = {
    vehicleId: number
    registration: string
    vehicleName: string | null
    clientVehicleDescription: string | null
    fuelTargetConsumption: string | null
    vehicleEngineTypeId: string | null
    maintenanceId: number | null
    defaultSiteLocationId: number | null
    isPoolActive: boolean
    maintenanceStatusId: number | null
    bookingVehicleTypeId: number
    vehicleStatusOptionId: number | null
    bookingAllocationPriority: number | null
    speedSourceId: number | null
    defaultDriver: string | null
    createdTs: string | null
    updatedTs: string | null
    departments: Array<{
      id: number
      name: string
    }>
    driverLicenses: Array<License>
    specialDriverLicenses: Array<License>
  }

  type Output = Array<Vehicle>
}

async function getVehiclesForDriver(driverId: DriverId | null) {
  if (driverId === null) return
  const result = await restGet<FetchVehiclesForDriver.Output>(
    `/scdf/vehicle/vehicle-for-driver?clientDriverId=${driverId}`,
  )
  return result.map((vehicle) => ({
    id: vehicle.vehicleId.toString() as VehicleId,
    registration: vehicle.registration,
    vehicle:
      !vehicle.vehicleName || isNilOrEmptyString(vehicle.vehicleName.trim())
        ? vehicle.registration
        : vehicle.vehicleName,
    bookingVehicleTypeId: vehicle.bookingVehicleTypeId.toString(),
    siteLocationId: vehicle.defaultSiteLocationId?.toString() ?? null,
    commonPool: vehicle.isPoolActive,
    departments: new Set(
      vehicle.departments.map((d) => d.id.toString() as CompanyDepartmentId),
    ),
  }))
}

export const vehiclesForDriverQueryKey = (driverId: DriverId | null) =>
  driverId
    ? (['scdf/vehicle/vehicle-for-driver', driverId] as const)
    : (['scdf/vehicle/vehicle-for-driver'] as const)

const vehicleForDriverQuery = (driverId: DriverId | null) =>
  createQuery({
    queryKey: vehiclesForDriverQueryKey(driverId),
    queryFn: () => getVehiclesForDriver(driverId),
    enabled: !isNilOrEmptyString(driverId),
    ...makeQueryErrorHandlerWithToast(),
  })

const useVehiclesForDriverQuery = (driverId: DriverId | null) =>
  useQuery(vehicleForDriverQuery(driverId))

export default useVehiclesForDriverQuery
