import { Chip, Stack, Typography, useTheme } from '@karoo-ui/core'
import WarningIcon from '@mui/icons-material/Warning'

import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'
import {
  BookingStatus,
  SCDFStatusTextMapping,
  SPFStatusTextMapping,
  StatusTextColorMapping,
} from '../utils/constants'
import { getStatusColorMapping } from '../utils/status-mapping'

type Props = {
  bookingStatusId: BookingStatus
  approvalProcess?: {
    approvalsNeeded: number | null
    approvedCount: `${number}` | null
  }
}

const CarpoolStatusChip = ({ bookingStatusId, approvalProcess }: Props) => {
  const companyName = useTypedSelector(getCompanyName)
  const isScdf = companyName === 'SCDF'

  const StatusTextMapping = isScdf ? SCDFStatusTextMapping : SPFStatusTextMapping

  const theme = useTheme()
  return (
    <Stack
      direction="row"
      spacing="5px"
      alignItems="center"
    >
      <Chip
        size="small"
        label={
          <Stack
            direction="row"
            spacing="2px"
            alignItems="center"
          >
            {[
              BookingStatus.BOOKING_STATUS_ACTIVE_LATE,
              BookingStatus.BOOKING_STATUS_RETURNED_LATE,
              BookingStatus.BOOKING_STATUS_FORCE_TERMINATED,
            ].includes(bookingStatusId) && (
              <WarningIcon
                sx={{
                  height: '18px',
                  color:
                    bookingStatusId !== BookingStatus.BOOKING_STATUS_FORCE_TERMINATED
                      ? '#F44336'
                      : '#FF9800',
                }}
              />
            )}
            <IntlTypography msgProps={{ id: StatusTextMapping[bookingStatusId] }} />
          </Stack>
        }
        sx={{
          backgroundColor: getStatusColorMapping(theme)[bookingStatusId],
          color: StatusTextColorMapping[bookingStatusId],
        }}
      />
      {[BookingStatus.BOOKING_STATUS_REQUESTED].includes(bookingStatusId) &&
        !!approvalProcess &&
        !!approvalProcess.approvalsNeeded &&
        approvalProcess.approvalsNeeded !== 1 && (
          <Typography variant="subtitle2">
            {`${ctIntl.formatMessage({ id: 'Approval' })}: `}
            {`${approvalProcess.approvedCount}/${approvalProcess.approvalsNeeded}`}
          </Typography>
        )}
    </Stack>
  )
}

export default CarpoolStatusChip
