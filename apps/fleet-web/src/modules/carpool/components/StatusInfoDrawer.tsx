import {
  <PERSON><PERSON>,
  <PERSON>,
  Chip,
  Drawer,
  Grid,
  IconB<PERSON>on,
  Stack,
  styled,
  Typography,
  type ChipOwnProps,
} from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import { Fragment } from 'react/jsx-runtime'

import IntlTypography from 'src/util-components/IntlTypography'

import { ctIntl } from 'cartrack-ui-kit'

type StatusInfoDrawerProps = {
  onClose: () => void
}

const statusData = [
  {
    status: 'Requested',
    color: 'warning',
    remarks: 'tfms.status.requested.remarks',
  },
  {
    status: 'global.approved',
    color: 'success',
    remarks: 'tfms.status.approved.remarks',
  },
  {
    status: 'scdf.status.declined',
    color: 'error',
    remarks: 'tfms.status.declined.remarks',
  },
  {
    status: 'Active',
    color: 'info',
    remarks: 'tfms.status.active.remarks',
  },
  {
    status: 'Completed',
    color: 'info',
    remarks: 'tfms.status.returned.remarks',
  },
  {
    status: 'Active Late',
    color: 'warning',
    remarks: 'tfms.status.activeLate.remarks',
  },
  {
    status: 'Completed Late',
    color: 'warning',
    remarks: 'tfms.status.returnedLate.remarks',
  },
  {
    status: 'tfms.status.forceTerminated',
    color: 'error',
    remarks: 'tfms.status.forceTerminated.remarks',
  },
  {
    status: 'Cancelled',
    color: 'default',
    remarks: 'tfms.status.cancelled.remarks',
  },
] as Array<{ status: string; color: ChipOwnProps['color']; remarks: string }>

const StatusInfoDrawer = ({ onClose }: StatusInfoDrawerProps) => (
  <Drawer
    anchor="right"
    open
    onClose={onClose}
    PaperProps={{ sx: { width: '500px' } }}
  >
    <Stack
      spacing={2}
      py={2}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        px={2}
      >
        <IntlTypography
          variant="h6"
          msgProps={{ id: 'tfms.statusDrawer.title' }}
        />
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Alert severity="info">
        <Typography>
          If you need to modify a booking that has already been approved, please contact
          the transport officer.
        </Typography>
      </Alert>
      <Grid
        container
        rowSpacing={2}
        px={2}
        sx={{
          '--Grid-borderWidth': '1px',
          '& > div': {
            borderBottom: 'var(--Grid-borderWidth) solid',
            borderColor: 'divider',
          },
        }}
      >
        <GridLeft size={4}>
          <IntlTypography
            variant="subtitle2"
            msgProps={{ id: 'tfms.statusDrawer.header.bookingStatus' }}
          />
        </GridLeft>
        <GridRight size={8}>
          <IntlTypography
            variant="subtitle2"
            msgProps={{ id: 'Remarks' }}
          />
        </GridRight>
        {statusData.map((item) => (
          <Fragment key={item.status}>
            <GridLeft size={4}>
              <Chip
                label={ctIntl.formatMessage({ id: item.status })}
                color={item.color}
                size="small"
              />
            </GridLeft>
            <GridRight size={8}>
              <IntlTypography msgProps={{ id: item.remarks }} />
            </GridRight>
          </Fragment>
        ))}
      </Grid>
    </Stack>
  </Drawer>
)

const GridLeft = styled(Grid)(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    alignItems: 'center',
    pb: theme.spacing(2),
    pl: theme.spacing(1),
  }),
)

const GridRight = styled(Grid)(({ theme }) =>
  theme.unstable_sx({
    pb: theme.spacing(2),
  }),
)

export default StatusInfoDrawer
