import { useCallback, useEffect, useMemo, useState } from 'react'
import { Box, Button, Menu, MenuItem, Paper, Stack } from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import SortIcon from '@mui/icons-material/Sort'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { DateTime } from 'luxon'
import { useHistory, useRouteMatch } from 'react-router'

import { buildRouteQueryString } from 'api/utils'
import { getSettings_UNSAFE } from 'duxs/user'
import { getCompanyName } from 'duxs/user-sensitive-selectors'
import { DETAILS_PREFIX } from 'src/modules/app/components/routes/carpool'
import { issuanceRequestSearchParamsSchema } from 'src/modules/carpool/components/SpfIssuanceRequestDrawer/schema'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'

import PanelMetric from '../components/PanelMetric'
import {
  SCDFStatusTextMapping,
  SPFStatusTextMapping,
  type BookingStatus,
} from '../utils/constants'
import type { FetchCarpoolEventList } from './api/types'
import useCarpoolEventListQuery, {
  type CarpoolEvent,
  type CarpoolVehicle,
} from './api/useCarpoolEventListQuery'
import { SortOptions, ViewOptions } from './constants'
import LeftPanel, { LeftPanelWidth } from './LeftPanel'

type Props = {
  selectedDate: DateTime
  selectedView: (typeof ViewOptions)[number]
  selectedSort: (typeof SortOptions)[number]
  selectedVehicles: Record<string, boolean>
  vehicles: Array<CarpoolVehicle>
  events: Array<CarpoolEvent>
  loadingEvents: boolean
  addNewRequest: (vehicleId: string) => void
  handleEditEvent: (e: CarpoolEvent) => void
}

const CalendarEvents = (
  Component: React.ComponentType<Props>,
  type: 'Calendar' | 'Resource',
) => {
  const CalendarEventsHOC = () => {
    const { path } = useRouteMatch()
    const history = useHistory()
    const { carpoolAppName } = useTypedSelector(getSettings_UNSAFE)

    const companyName = useTypedSelector(getCompanyName)
    const isScdf = companyName === 'SCDF'

    const StatusTextMapping = isScdf ? SCDFStatusTextMapping : SPFStatusTextMapping
    const [selectedDate, setSelectedDate] = useState(DateTime.local())
    const [selectedView, setSelectedView] = useState<(typeof ViewOptions)[number]>(
      ViewOptions[1],
    )
    const [selectedSort, setSelectedSort] = useState<(typeof SortOptions)[number]>(
      SortOptions[0],
    )
    const [selectedVehicles, setSelectedVehicles] = useState<Record<string, boolean>>(
      {},
    )
    const [selectedVehicleTypes, setSelectedVehicleTypes] = useState<
      Record<string, boolean>
    >({})
    const [selectedDrivers, setSelectedDrivers] = useState<Record<string, boolean>>({})

    const getDateRange = useCallback(
      () => ({
        startDate: selectedDate
          .startOf(selectedView === 'Day' ? 'day' : 'week')
          .toISODate(),
        endDate: selectedDate
          .endOf(selectedView === 'Day' ? 'day' : 'week')
          .toISODate(),
      }),
      [selectedDate, selectedView],
    )

    const [dateRange, setDateRange] =
      useState<FetchCarpoolEventList.ApiParams>(getDateRange())

    useEffect(() => {
      setDateRange(getDateRange())
    }, [getDateRange])

    const carpoolEventListQuery = useCarpoolEventListQuery(dateRange)

    const filteredVehicles = useMemo(() => {
      if (!carpoolEventListQuery.data) {
        return []
      }

      const selectAllVehicles = Object.values(selectedVehicles).every((o) => o === true)
      const selectAllVehicleTypes = Object.values(selectedVehicleTypes).every(
        (o) => o === true,
      )
      const selectAllDrivers = Object.values(selectedDrivers).every((o) => o === true)
      const vehicles = carpoolEventListQuery.data.filter(
        (e) =>
          (selectAllVehicles || selectedVehicles[e.vehicleId]) &&
          (selectAllVehicleTypes || selectedVehicleTypes[e.bookingVehicleTypeId]),
      )

      if (selectAllDrivers) {
        return vehicles
      }

      return vehicles.map((v) => ({
        ...v,
        events: v.events.filter((e) => selectedDrivers[e.requestClientDriverId]),
      }))
    }, [
      carpoolEventListQuery.data,
      selectedVehicles,
      selectedVehicleTypes,
      selectedDrivers,
    ])

    const filteredEvents = useMemo(
      () =>
        filteredVehicles.reduce(
          (acc, vehicle) => [...acc, ...vehicle.events],
          [] as Array<CarpoolEvent>,
        ),
      [filteredVehicles],
    )

    const metrics = useMemo(
      () =>
        filteredEvents.reduce(
          (acc, event) => {
            if (acc[event.bookingStatusId]) {
              acc[event.bookingStatusId] = acc[event.bookingStatusId] + 1
            } else {
              acc[event.bookingStatusId] = 1
            }
            return acc
          },
          {} as Record<BookingStatus, number>,
        ),
      [filteredEvents],
    )

    const handleEditEvent = (event: CarpoolEvent) => {
      history.push(
        `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
          schema: issuanceRequestSearchParamsSchema,
          searchParams: {
            type: 'edit',
            id: event.bookingId,
          },
        })}`,
      )
    }

    return (
      <Box
        sx={{
          display: 'flex',
          flexFlow: 'column',
          height: '100%',
          width: '100%',
          p: 2,
          gap: 2,
          position: 'relative',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <IntlTypography
            variant="h5"
            msgProps={{
              id:
                type === 'Calendar'
                  ? 'carpool.calendar.title'
                  : 'carpool.resources.title',
              values: { carpoolAppName },
            }}
          />

          <Stack
            direction="row"
            spacing={1}
          >
            {type === 'Resource' && (
              <>
                <MenuButton
                  selected={selectedView}
                  setSelected={setSelectedView}
                  options={ViewOptions}
                  title="View"
                  icon={<VisibilityIcon />}
                />
                <MenuButton
                  selected={selectedSort}
                  setSelected={setSelectedSort}
                  options={SortOptions}
                  title="Sort"
                  icon={<SortIcon />}
                />
              </>
            )}

            <Button
              variant="contained"
              onClick={() =>
                history.push(
                  `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                    schema: issuanceRequestSearchParamsSchema,
                    searchParams: {
                      type: 'add',
                    },
                  })}`,
                )
              }
              startIcon={<AddIcon />}
            >
              {ctIntl.formatMessage(
                { id: 'carpool.newCarpoolRequest' },
                { values: { carpoolAppName } },
              )}
            </Button>
          </Stack>
        </Box>
        <Paper
          sx={{
            display: 'flex',
            gap: 7,
            width: '100%',
            bgcolor: 'primary.dark',
            py: 2,
            px: 3,
          }}
        >
          <PanelMetric
            labelProps={{ msgProps: { id: 'Total' } }}
            value={filteredEvents.length}
          />
          {Object.keys(metrics).map((key) => (
            <PanelMetric
              key={key}
              labelProps={{
                msgProps: { id: StatusTextMapping[key as BookingStatus] },
              }}
              value={metrics[key as keyof typeof metrics]}
            />
          ))}
        </Paper>
        <Box
          sx={{
            display: 'flex',
            gap: 3,
            height: '100%',
          }}
        >
          <Box
            sx={{
              height: '100%',
              overflow: 'hidden',
              position: 'relative',
              width: '100%',
            }}
          >
            <LeftPanel
              selectedDate={selectedDate}
              setSelectedDate={setSelectedDate}
              selectedVehicles={selectedVehicles}
              setSelectedVehicles={setSelectedVehicles}
              selectedVehicleTypes={selectedVehicleTypes}
              setSelectedVehicleTypes={setSelectedVehicleTypes}
              selectedDrivers={selectedDrivers}
              setSelectedDrivers={setSelectedDrivers}
            />
            <Box
              sx={{
                pb: 2,
                position: 'absolute',
                left: `calc(${LeftPanelWidth} + 16px)`,
                width: `calc(100% - ${LeftPanelWidth} - 16px)`,
                height: '100%',
              }}
            >
              <Component
                selectedDate={selectedDate}
                selectedView={selectedView}
                selectedSort={selectedSort}
                selectedVehicles={selectedVehicles}
                events={filteredEvents}
                vehicles={filteredVehicles}
                loadingEvents={carpoolEventListQuery.isPending}
                // Vehicle pre-selection is no longer used,
                // as vehicles options will be filtered after driver selection.
                addNewRequest={(_vehicleId) =>
                  history.push(
                    `${path}/${DETAILS_PREFIX}?${buildRouteQueryString({
                      schema: issuanceRequestSearchParamsSchema,
                      searchParams: {
                        type: 'add',
                      },
                    })}`,
                  )
                }
                handleEditEvent={handleEditEvent}
              />
            </Box>
          </Box>
        </Box>
      </Box>
    )
  }

  return CalendarEventsHOC
}

export default CalendarEvents

type MenuButtonProps<T> = {
  selected: T
  setSelected: (option: T) => void
  options: ReadonlyArray<T>
  title: string
  icon: React.ReactNode
}

const MenuButton = <T,>({
  selected,
  setSelected,
  options,
  title,
  icon,
}: MenuButtonProps<T>) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleViewMenuClose = () => {
    setAnchorEl(null)
  }

  const handleViewMenuSelect = (option: T) => {
    setSelected(option)
    setAnchorEl(null)
  }

  return (
    <>
      <Button
        variant="outlined"
        startIcon={icon}
        color="secondary"
        onClick={handleClick}
      >
        {ctIntl.formatMessage({ id: title })}
      </Button>
      <Menu
        id="view-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleViewMenuClose}
        MenuListProps={{
          'aria-labelledby': 'view-button',
        }}
      >
        {options.map((option) => (
          <MenuItem
            key={option as string}
            onClick={() => handleViewMenuSelect(option)}
            selected={option === selected}
            sx={{
              fontWeight: option === selected ? 'bold' : 'normal',
            }}
          >
            {ctIntl.formatMessage({ id: option as string })}
          </MenuItem>
        ))}
      </Menu>
    </>
  )
}
