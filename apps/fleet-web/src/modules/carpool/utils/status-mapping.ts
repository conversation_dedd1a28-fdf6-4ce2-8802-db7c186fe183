import type { KarooUiInternalTheme } from '@karoo-ui/core'

import { BookingStatus } from './constants'

export const getStatusColorMapping = (
  theme: KarooUiInternalTheme,
): Record<BookingStatus, string> => ({
  [BookingStatus.BOOKING_STATUS_FREE]: theme.palette.grey[50],
  [BookingStatus.BOOKING_STATUS_REQUESTED]: theme.palette.warning.main,
  [BookingStatus.BOOKING_STATUS_APPROVED]: theme.palette.success.main,
  [BookingStatus.BOOKING_STATUS_DECLINED]: theme.palette.error.main,
  [BookingStatus.BOOKING_STATUS_CANCELLED]: theme.palette.grey[50],
  [BookingStatus.BOOKING_STATUS_ACTIVE]: theme.palette.info.main,
  [BookingStatus.BOOKING_STATUS_ACTIVE_LATE]: theme.palette.warning.main,
  [BookingStatus.BOOKING_STATUS_RETURNED]: theme.palette.info.main,
  [BookingStatus.BOOKING_STATUS_RETURNED_LATE]: theme.palette.warning.main,
  [BookingStatus.BOOKING_STATUS_FORCE_TERMINATED]: theme.palette.error.main,
})
