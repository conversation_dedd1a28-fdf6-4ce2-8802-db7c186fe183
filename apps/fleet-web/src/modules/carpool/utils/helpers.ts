import { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { messages } from 'src/shared/formik'

import { CarpoolRules } from './constants'

export const dateFormattingFromSQL = (dateString: string) =>
  dateString && DateTime.fromSQL(dateString).toFormat('D t')

export const showRequiredMessageOnAllErrorTypes = () => ({ message: messages.required })

export const mapRulesToTranslationKeys = (rule: CarpoolRules, isSPF: boolean) =>
  match(rule)
    .with(
      CarpoolRules.BOOKING_RULE_ADVANCE_BOOKING,
      () => 'carpool.settings.rules.advanceBooking',
    )
    .with(
      CarpoolRules.BOOKING_RULE_CHECK_DRIVER_LICENSE_CLASS,
      () => 'carpool.settings.rules.checkDriverLicenseClass',
    )
    .with(CarpoolRules.BOOKING_RULE_CHECK_DRIVER_LICENSE_TYPE, () =>
      isSPF
        ? 'carpool.settings.rules.checkDriverPoliceLicenseType'
        : 'carpool.settings.rules.checkDriverSpecialLicenseType',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_ALLOCATION,
      () => 'carpool.settings.rules.emailToDriverOnVehicleAllocation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_APPROVAL,
      () => 'carpool.settings.rules.emailToDriverOnRequestApproval',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_CANCELLATION,
      () => 'carpool.settings.rules.emailToDriverOnRequestCancellation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_NOTIFICATION_SEND_TERMS_CONDITIONS,
      () => 'carpool.settings.rules.sendTnCToDriver',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_SMS_NOTIFICATION_ALLOCATION,
      () => 'carpool.settings.rules.smsToDriverOnVehicleAllocation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_SMS_NOTIFICATION_APPROVAL,
      () => 'carpool.settings.rules.smsToDriverOnRequestApproval',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DRIVER_SMS_NOTIFICATION_CANCELLATION,
      () => 'carpool.settings.rules.smsToDriverOnRequestCancellation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_UNIT_MANAGER_SMS_NOTIFICATION_ALLOCATION,
      () => 'carpool.settings.rules.smsToManagerOnRequestAllocation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_UNIT_MANAGER_EMAIL_NOTIFICATION_ALLOCATION,
      () => 'carpool.settings.rules.emailToManagerOnRequestAllocation',
    )
    .with(
      CarpoolRules.BOOKING_RULE_KEY_ACTIVATION,
      () => 'carpool.settings.rules.activateBooking',
    )
    .with(
      CarpoolRules.BOOKING_RULE_KEY_COLLECTION,
      () => 'carpool.settings.rules.keyCollection',
    )
    .with(
      CarpoolRules.BOOKING_RULE_MANAGER_EMAIL_NOTIFICATION_APPROVAL,
      () => 'carpool.settings.rules.emailToManagersOnRequestApproval',
    )
    .with(
      CarpoolRules.BOOKING_RULE_MANAGER_SMS_NOTIFICATION_APPROVAL,
      () => 'carpool.settings.rules.smsToManagersOnRequestApproval',
    )
    .with(
      CarpoolRules.BOOKING_RULE_MAX_BOOKING_TIME,
      () => 'carpool.settings.rules.maximumBookingTime',
    )
    .with(
      CarpoolRules.BOOKING_RULE_AUTO_APPROVE_NEW_REQUEST,
      () => 'carpool.settings.rules.autoApproveNewRequest',
    )
    .with(
      CarpoolRules.BOOKING_RULE_DISABLE_MULTI_LEVEL_APPROVAL,
      () => 'carpool.settings.rules.disableMultiLevelApproval',
    )
    .with(
      CarpoolRules.BOOKING_RULE_MANDATORY_DRIVER_IN_REQUEST,
      () => 'carpool.settings.rules.driverMandatoryForRequest',
    )
    .with(
      CarpoolRules.BOOKING_RULE_PREDRIVE_CHECKLIST_AVAILABLE_TIME,
      () => 'carpool.settings.rules.preDriveChecklistAvailableTime',
    )
    .otherwise((ruleName) => ruleName)
