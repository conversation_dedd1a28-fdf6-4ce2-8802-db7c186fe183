export enum BookingStatus {
  BOOKING_STATUS_FREE = '1',
  BOOKING_STATUS_REQUESTED = '2',
  BOOKING_STATUS_APPROVED = '3',
  BOOKING_STATUS_DECLINED = '4',
  BOOKING_STATUS_CANCELLED = '5',
  BOOKING_STATUS_ACTIVE = '6',
  BOOKING_STATUS_ACTIVE_LATE = '8',
  BOOKING_STATUS_RETURNED = '9',
  BOOKING_STATUS_RETURNED_LATE = '10',
  BOOKING_STATUS_FORCE_TERMINATED = '12',
}

export type BookingStatusType = keyof typeof BookingStatus

export enum BookingDetailsEventStatus {
  REQUESTED = 'REQUESTED',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  ACTIVE = 'ACTIVE',
  ACTIVE_LATE = 'ACTIVE_LATE',
  ACTIVE_ALMOST_LATE = 'ACTIVE_ALMOST_LATE',
  KEY_COLLECTED = 'KEY_COLLECTED',
  RETURNED = 'RETURNED',
  RETURNED_LATE = 'RETURNED_LATE',
  LEFT_PICK_UP = 'LEFT_PICK_UP',
  ENTERED_DROP_OFF = 'ENTERED_DROP_OFF',
  KEY_RETURNED = 'KEY_RETURNED',
  CANCELLED = 'CANCELLED',
  FORCE_TERMINATED = 'FORCE_TERMINATED',
}

export enum CustomTabs {
  APPROVED = 'approved',
  REQUESTED = 'requested',
  DECLINED = 'declined',
  CANCELLED = 'cancelled',
  ACTIVE = 'active',
  ACTIVE_LATE = 'activeLate',
  FREE = 'free',
  RETURNED = 'returned',
  RETURNED_LATE = 'returnedLate',
  FORCE_TERMINATED = 'forceTerminated',
}

export const BookingStatusMapping = {
  free: ['BOOKING_STATUS_FREE'],
  requested: ['BOOKING_STATUS_REQUESTED', 'BOOKING_STATUS_APPROVED'],
  active: ['BOOKING_STATUS_ACTIVE', 'BOOKING_STATUS_ACTIVE_LATE'],
  issued: [
    'BOOKING_STATUS_RETURNED',
    'BOOKING_STATUS_RETURNED_LATE',
    'BOOKING_STATUS_DECLINED',
    'BOOKING_STATUS_CANCELLED',
  ],
} as const satisfies Record<string, Array<BookingStatusType>>

export enum MaintenanceEventStatus {
  AWAITING_REPAIR = 'AWAITING_REPAIR',
  REPAIRED = 'REPAIRED',
  PASSED_INSPECTION = 'PASSED_INSPECTION',
  FAILED_INSPECTION = 'FAILED_INSPECTION',
}

export enum CarpoolRules {
  BOOKING_RULE_KEY_ACTIVATION = 'BOOKING_RULE_KEY_ACTIVATION',
  BOOKING_RULE_KEY_COLLECTION = 'BOOKING_RULE_KEY_COLLECTION',
  BOOKING_RULE_MANAGER_EMAIL_NOTIFICATION_APPROVAL = 'BOOKING_RULE_MANAGER_EMAIL_NOTIFICATION_APPROVAL',
  BOOKING_RULE_DRIVER_NOTIFICATION_SEND_TERMS_CONDITIONS = 'BOOKING_RULE_DRIVER_NOTIFICATION_SEND_TERMS_CONDITIONS',
  BOOKING_RULE_MANAGER_SMS_NOTIFICATION_APPROVAL = 'BOOKING_RULE_MANAGER_SMS_NOTIFICATION_APPROVAL',
  BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_CANCELLATION = 'BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_CANCELLATION',
  BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_APPROVAL = 'BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_APPROVAL',
  BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_ALLOCATION = 'BOOKING_RULE_DRIVER_EMAIL_NOTIFICATION_ALLOCATION',
  BOOKING_RULE_DRIVER_SMS_NOTIFICATION_CANCELLATION = 'BOOKING_RULE_DRIVER_SMS_NOTIFICATION_CANCELLATION',
  BOOKING_RULE_DRIVER_SMS_NOTIFICATION_ALLOCATION = 'BOOKING_RULE_DRIVER_SMS_NOTIFICATION_ALLOCATION',
  BOOKING_RULE_DRIVER_SMS_NOTIFICATION_APPROVAL = 'BOOKING_RULE_DRIVER_SMS_NOTIFICATION_APPROVAL',
  BOOKING_RULE_UNIT_MANAGER_SMS_NOTIFICATION_ALLOCATION = 'BOOKING_RULE_UNIT_MANAGER_SMS_NOTIFICATION_ALLOCATION',
  BOOKING_RULE_UNIT_MANAGER_EMAIL_NOTIFICATION_ALLOCATION = 'BOOKING_RULE_UNIT_MANAGER_EMAIL_NOTIFICATION_ALLOCATION',
  BOOKING_RULE_CHECK_DRIVER_LICENSE_CLASS = 'BOOKING_RULE_CHECK_DRIVER_LICENSE_CLASS',
  BOOKING_RULE_CHECK_DRIVER_LICENSE_TYPE = 'BOOKING_RULE_CHECK_DRIVER_LICENSE_TYPE',
  BOOKING_RULE_ADVANCE_BOOKING = 'BOOKING_RULE_ADVANCE_BOOKING',
  BOOKING_RULE_MAX_BOOKING_TIME = 'BOOKING_RULE_MAX_BOOKING_TIME',
  BOOKING_RULE_AUTO_APPROVE_NEW_REQUEST = 'BOOKING_RULE_AUTO_APPROVE_NEW_REQUEST',
  BOOKING_RULE_DISABLE_MULTI_LEVEL_APPROVAL = 'BOOKING_RULE_DISABLE_MULTI_LEVEL_APPROVAL',
  BOOKING_RULE_MANDATORY_DRIVER_IN_REQUEST = 'BOOKING_RULE_MANDATORY_DRIVER_IN_REQUEST',
  BOOKING_RULE_PREDRIVE_CHECKLIST_AVAILABLE_TIME = 'BOOKING_RULE_PREDRIVE_CHECKLIST_AVAILABLE_TIME',
}

export const SPFStatusTextMapping: Record<BookingStatus, string> = {
  [BookingStatus.BOOKING_STATUS_FREE]: 'Free',
  [BookingStatus.BOOKING_STATUS_REQUESTED]: 'Requested',
  [BookingStatus.BOOKING_STATUS_APPROVED]: 'global.approved',
  [BookingStatus.BOOKING_STATUS_DECLINED]: 'Declined',
  [BookingStatus.BOOKING_STATUS_CANCELLED]: 'Cancelled',
  [BookingStatus.BOOKING_STATUS_ACTIVE]: 'Active',
  [BookingStatus.BOOKING_STATUS_ACTIVE_LATE]: 'Active Late',
  [BookingStatus.BOOKING_STATUS_RETURNED]: 'Returned',
  [BookingStatus.BOOKING_STATUS_RETURNED_LATE]: 'Returned Late',
  [BookingStatus.BOOKING_STATUS_FORCE_TERMINATED]: 'tfms.status.forceTerminated',
}

export const SCDFStatusTextMapping: Record<BookingStatus, string> = {
  [BookingStatus.BOOKING_STATUS_FREE]: 'Free',
  [BookingStatus.BOOKING_STATUS_REQUESTED]: 'Requested',
  [BookingStatus.BOOKING_STATUS_APPROVED]: 'global.approved',
  [BookingStatus.BOOKING_STATUS_DECLINED]: 'scdf.status.declined',
  [BookingStatus.BOOKING_STATUS_CANCELLED]: 'Cancelled',
  [BookingStatus.BOOKING_STATUS_ACTIVE]: 'Active',
  [BookingStatus.BOOKING_STATUS_ACTIVE_LATE]: 'Active Late',
  [BookingStatus.BOOKING_STATUS_RETURNED]: 'Completed',
  [BookingStatus.BOOKING_STATUS_RETURNED_LATE]: 'Completed Late',
  [BookingStatus.BOOKING_STATUS_FORCE_TERMINATED]: 'tfms.status.forceTerminated',
}

export const SPFBookingDetailsEventStatusTextMapping: Record<
  BookingDetailsEventStatus,
  string
> = {
  [BookingDetailsEventStatus.ACTIVE]: 'Active',
  [BookingDetailsEventStatus.ACTIVE_ALMOST_LATE]: 'Active Almost Late',
  [BookingDetailsEventStatus.ACTIVE_LATE]: 'Active Late',
  [BookingDetailsEventStatus.APPROVED]: 'Approved',
  [BookingDetailsEventStatus.CANCELLED]: 'Cancelled',
  [BookingDetailsEventStatus.DECLINED]: 'Declined',
  [BookingDetailsEventStatus.ENTERED_DROP_OFF]: 'Enter Dropoff',
  [BookingDetailsEventStatus.KEY_COLLECTED]: 'Key Collected',
  [BookingDetailsEventStatus.KEY_RETURNED]: 'Key Returned',
  [BookingDetailsEventStatus.LEFT_PICK_UP]: 'Pickup Left',
  [BookingDetailsEventStatus.REQUESTED]: 'Requested',
  [BookingDetailsEventStatus.RETURNED]: 'Returned',
  [BookingDetailsEventStatus.RETURNED_LATE]: 'Returned Late',
  [BookingDetailsEventStatus.FORCE_TERMINATED]: 'Force Terminated',
}

export const SCDFBookingDetailsEventStatusTextMapping: Record<
  BookingDetailsEventStatus,
  string
> = {
  [BookingDetailsEventStatus.ACTIVE]: 'Active',
  [BookingDetailsEventStatus.ACTIVE_ALMOST_LATE]: 'Active Almost Late',
  [BookingDetailsEventStatus.ACTIVE_LATE]: 'Active Late',
  [BookingDetailsEventStatus.APPROVED]: 'Approved',
  [BookingDetailsEventStatus.CANCELLED]: 'Cancelled',
  [BookingDetailsEventStatus.DECLINED]: 'scdf.status.declined',
  [BookingDetailsEventStatus.ENTERED_DROP_OFF]: 'Enter Dropoff',
  [BookingDetailsEventStatus.KEY_COLLECTED]: 'Key Collected',
  [BookingDetailsEventStatus.KEY_RETURNED]: 'Key Returned',
  [BookingDetailsEventStatus.LEFT_PICK_UP]: 'Pickup Left',
  [BookingDetailsEventStatus.REQUESTED]: 'Requested',
  [BookingDetailsEventStatus.RETURNED]: 'Completed',
  [BookingDetailsEventStatus.RETURNED_LATE]: 'Completed Late',
  [BookingDetailsEventStatus.FORCE_TERMINATED]: 'Force Terminated',
}

export const StatusTextColorMapping: Record<BookingStatus, string> = {
  [BookingStatus.BOOKING_STATUS_FREE]: '#FFF',
  [BookingStatus.BOOKING_STATUS_REQUESTED]: '#FFF',
  [BookingStatus.BOOKING_STATUS_APPROVED]: '#FFF',
  [BookingStatus.BOOKING_STATUS_DECLINED]: '#FFF',
  [BookingStatus.BOOKING_STATUS_CANCELLED]: '#000',
  [BookingStatus.BOOKING_STATUS_ACTIVE]: '#FFF',
  [BookingStatus.BOOKING_STATUS_ACTIVE_LATE]: '#FFF',
  [BookingStatus.BOOKING_STATUS_RETURNED]: '#FFF',
  [BookingStatus.BOOKING_STATUS_RETURNED_LATE]: '#FFF',
  [BookingStatus.BOOKING_STATUS_FORCE_TERMINATED]: '#FFF',
}

export const MaintenanceEventStatusTextMapping: Record<MaintenanceEventStatus, string> =
  {
    [MaintenanceEventStatus.AWAITING_REPAIR]: 'maintenance.eventStatus.awaitingRepair',
    [MaintenanceEventStatus.FAILED_INSPECTION]:
      'maintenance.eventStatus.failedInspection',
    [MaintenanceEventStatus.PASSED_INSPECTION]:
      'maintenance.eventStatus.passedInspection',
    [MaintenanceEventStatus.REPAIRED]: 'maintenance.eventStatus.repaired',
  }

export const MaintenanceEventStatusColorMapping: Record<
  MaintenanceEventStatus,
  'warning' | 'error' | 'success'
> = {
  [MaintenanceEventStatus.AWAITING_REPAIR]: 'warning',
  [MaintenanceEventStatus.FAILED_INSPECTION]: 'error',
  [MaintenanceEventStatus.PASSED_INSPECTION]: 'success',
  [MaintenanceEventStatus.REPAIRED]: 'success',
}

export const EventStatusColorMapping: Record<BookingDetailsEventStatus, string> = {
  [BookingDetailsEventStatus.REQUESTED]: '#F57C00',
  [BookingDetailsEventStatus.APPROVED]: '#4CAF50',
  [BookingDetailsEventStatus.DECLINED]: '#F44336',
  [BookingDetailsEventStatus.ACTIVE]: '#2196F3',
  [BookingDetailsEventStatus.ACTIVE_ALMOST_LATE]: '#2196F3',
  [BookingDetailsEventStatus.ACTIVE_LATE]: '#2196F3',
  [BookingDetailsEventStatus.KEY_COLLECTED]: '#2196F3',
  [BookingDetailsEventStatus.RETURNED]: '#64B5F6',
  [BookingDetailsEventStatus.RETURNED_LATE]: '#F44336',
  [BookingDetailsEventStatus.LEFT_PICK_UP]: '#2196F3',
  [BookingDetailsEventStatus.ENTERED_DROP_OFF]: '#2196F3',
  [BookingDetailsEventStatus.KEY_RETURNED]: '#64B5F6',
  [BookingDetailsEventStatus.CANCELLED]: '#E0E0E0',
  [BookingDetailsEventStatus.FORCE_TERMINATED]: '#F44336',
}

export const EventStatusTextColorMapping: Record<BookingDetailsEventStatus, string> = {
  [BookingDetailsEventStatus.REQUESTED]: '#FFF',
  [BookingDetailsEventStatus.APPROVED]: '#FFF',
  [BookingDetailsEventStatus.DECLINED]: '#FFF',
  [BookingDetailsEventStatus.ACTIVE]: '#FFF',
  [BookingDetailsEventStatus.ACTIVE_ALMOST_LATE]: '#FFF',
  [BookingDetailsEventStatus.ACTIVE_LATE]: '#FFF',
  [BookingDetailsEventStatus.KEY_COLLECTED]: '#FFF',
  [BookingDetailsEventStatus.RETURNED]: '#FFF',
  [BookingDetailsEventStatus.RETURNED_LATE]: '#FFF',
  [BookingDetailsEventStatus.LEFT_PICK_UP]: '#FFF',
  [BookingDetailsEventStatus.ENTERED_DROP_OFF]: '#FFF',
  [BookingDetailsEventStatus.KEY_RETURNED]: '#FFF',
  [BookingDetailsEventStatus.CANCELLED]: '#000',
  [BookingDetailsEventStatus.FORCE_TERMINATED]: '#FFF',
}
