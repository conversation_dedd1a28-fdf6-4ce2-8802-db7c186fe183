import { zodResolver } from '@hookform/resolvers/zod'
import {
  Autocomplete,
  KarooFormStateContextProvider,
  ListItem,
  Stack,
  styled,
  TextField,
} from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import type { QueryObserverSuccessResult } from '@tanstack/react-query'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'

import type { VehicleId } from 'api/types'
import { getActiveDriverOptions } from 'duxs/drivers'
import { getGeofenceOptions } from 'duxs/geofences'
import {
  getAllowMaxSpeed,
  getIsUserDistanceInMiles,
  getTimeZonesOptions,
} from 'duxs/user'
import { getVehiclesEditVehicleSetting } from 'duxs/user-sensitive-selectors'
import { getVehicleTypeOptions } from 'duxs/vehicles'
import { colors } from 'src/api/colors'
import type { FetchVehicleDetailsQuery } from 'src/api/vehicles/useVehicleDetailsQuery'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import FormSection from 'src/components/_form/FormSection'
import { useTypedSelector } from 'src/redux-hooks'
import { messages } from 'src/shared/formik'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import useUpdateVehicleSettingsMutation from './useUpdateVehicleSettingsMutation'

const generalSettingsValidationSchema = z.object({
  defaultDriver: z.string(),
  defaultTimeZone: z.string(),
  homeGeofence: z.string(),
  iconColor: z.string(),
  maxSpeed: z.string(),
  monthlyMileageLimit: z.string(),
  tollingTagId: z.string(),
  type: z.string().min(1, { message: messages.required }),
})

const parseVehicleDetailToString = ({ field }: { field: unknown }): string =>
  safeParseFromZodSchema(
    z
      .string()
      .or(z.number())
      .transform((f) => String(f).trim()),
    field,
    { defaultValue: () => '' },
  )

type Props = {
  vehicleId: VehicleId
  vehicleDetailsQuery: QueryObserverSuccessResult<FetchVehicleDetailsQuery.Return>
}

const GeneralSettings = ({ vehicleDetailsQuery, vehicleId }: Props) => {
  const vehicleDetails = vehicleDetailsQuery.data.fleetVehicle

  const allowMaxSpeed = useTypedSelector(getAllowMaxSpeed)
  const timezoneOptions = useTypedSelector(getTimeZonesOptions)
  const shouldUseMiles = useTypedSelector(getIsUserDistanceInMiles)
  const vehicleTypeOptions = useTypedSelector(getVehicleTypeOptions)
  const defaultDriverOptions = useTypedSelector(getActiveDriverOptions)
  const geofenceOptions = useTypedSelector(getGeofenceOptions)
  const vehiclesEditVehicleSetting = useTypedSelector(getVehiclesEditVehicleSetting)

  const updateVehicleSettings = useUpdateVehicleSettingsMutation()

  const { control, formState, handleSubmit, reset } = useForm<
    z.infer<typeof generalSettingsValidationSchema>
  >({
    resolver: zodResolver(generalSettingsValidationSchema),
    mode: 'all',
    values: {
      defaultDriver: parseVehicleDetailToString({
        field: vehicleDetails.defaultDriver,
      }),
      defaultTimeZone: parseVehicleDetailToString({
        field: vehicleDetails.defaultTimeZone,
      }),
      homeGeofence: parseVehicleDetailToString({
        field: vehicleDetails.homeGeofence,
      }),
      iconColor: parseVehicleDetailToString({ field: vehicleDetails.iconColor }),
      maxSpeed: parseVehicleDetailToString({ field: vehicleDetails.maxSpeed }),
      monthlyMileageLimit: parseVehicleDetailToString({
        field: vehicleDetails.monthlyMileageLimit,
      }),
      tollingTagId: parseVehicleDetailToString({
        field: vehicleDetails.tollingTagId,
      }),
      type: parseVehicleDetailToString({ field: vehicleDetails.type }),
    },
  })

  const submitForm = handleSubmit((data) => {
    updateVehicleSettings.mutate(
      { vehicleId, data, settingType: 'general' },
      {
        onSuccess(_, variables) {
          if (variables.settingType === 'general') {
            reset(variables.data)
          }
        },
      },
    )
  })

  return (
    <FormSection
      label="General Settings"
      mutationStatus={updateVehicleSettings.status}
      onSaveButtonClick={submitForm}
      onCancelButtonClick={reset}
      formState={formState}
      disabled={!vehiclesEditVehicleSetting}
    >
      {(isEditing) => (
        <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
          <Stack gap={2}>
            <Stack
              direction="row"
              gap={2}
            >
              <Controller
                control={control}
                name="defaultTimeZone"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    {...getAutocompleteVirtualizedProps({
                      options: timezoneOptions || [],
                    })}
                    fullWidth
                    value={
                      timezoneOptions.find((tz) => tz.value === field.value) || null
                    }
                    onChange={(_event, option) => {
                      field.onChange(option && option.value ? option.value : '')
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Default Time Zone' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
              {allowMaxSpeed && (
                <TextFieldControlled
                  fullWidth
                  slotProps={{
                    input: { inputMode: 'numeric' },
                  }}
                  ControllerProps={{
                    control,
                    name: 'maxSpeed',
                  }}
                  label={`${ctIntl.formatMessage({
                    id: 'Max Speed',
                  })} (${ctIntl.formatMessage({
                    id: shouldUseMiles
                      ? 'units.distancePerHour.mile'
                      : 'units.distancePerHour',
                  })})`}
                  onChange={({ target: { value } }, { controller }) => {
                    const trimmedValue = value.replaceAll(/\s/g, '')

                    // Accept 7 digits at most
                    // eslint-disable-next-line sonarjs/concise-regex
                    if (/^[0-9]{0,7}$/.test(trimmedValue)) {
                      const maxSpeed = Number(trimmedValue)

                      if (maxSpeed >= 0) {
                        controller.onChange(String(maxSpeed))
                      }
                    }
                  }}
                />
              )}
            </Stack>

            <Stack
              direction="row"
              gap={2}
            >
              <TextFieldControlled
                sx={{ width: '100%' }}
                slotProps={{ input: { inputMode: 'numeric' } }}
                ControllerProps={{ control, name: 'monthlyMileageLimit' }}
                label={ctIntl.formatMessage({ id: 'Monthly Mileage Limit' })}
                onChange={({ target: { value } }, { controller }) => {
                  const trimmedValue = value.replaceAll(/\s/g, '')

                  // Accept 7 digits at most
                  // eslint-disable-next-line sonarjs/concise-regex
                  if (/^[0-9]{0,7}$/.test(trimmedValue)) {
                    const numberLimit = Number(trimmedValue)

                    if (numberLimit >= 0) {
                      controller.onChange(String(numberLimit))
                    }
                  }
                }}
              />
              <Controller
                control={control}
                name="type"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    disableClearable
                    sx={{ width: '100%' }}
                    {...getAutocompleteVirtualizedProps({
                      options: vehicleTypeOptions || [],
                    })}
                    value={vehicleTypeOptions.find((tz) => tz.value === field.value)}
                    onChange={(_event, option) => {
                      field.onChange(option.value)
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        required
                        label={ctIntl.formatMessage({ id: 'Vehicle Type Icon' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
            </Stack>
            <Stack
              direction="row"
              gap={2}
            >
              <Controller
                control={control}
                name="iconColor"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    sx={{ width: '100%' }}
                    options={colors}
                    value={colors.find((c) => c.label === field.value) || null}
                    onChange={(_event, option) => {
                      field.onChange(option?.label || '')
                    }}
                    renderOption={(props, color) => (
                      <ListItem
                        {...props}
                        key={color.label}
                      >
                        <ColorIcon color={color.hex} />
                        <IntlTypography
                          variant="inherit"
                          msgProps={{ id: color.label }}
                        />
                      </ListItem>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Icon Color' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
              <Controller
                control={control}
                name="defaultDriver"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    sx={{ width: '100%' }}
                    {...getAutocompleteVirtualizedProps({
                      options: defaultDriverOptions || [],
                    })}
                    value={
                      defaultDriverOptions.find(
                        (driver) => driver.value === field.value,
                      ) || null
                    }
                    onChange={(_event, option) => {
                      field.onChange(option && option.value ? option.value : '')
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Default Driver' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
            </Stack>
            <Stack
              direction="row"
              gap={2}
            >
              <Controller
                control={control}
                name="homeGeofence"
                render={({ field, fieldState }) => (
                  <Autocomplete
                    sx={{ width: '100%' }}
                    {...getAutocompleteVirtualizedProps({
                      options: geofenceOptions || [],
                    })}
                    value={
                      geofenceOptions.find((geo) => geo.value === field.value) || null
                    }
                    onChange={(_event, option) => {
                      field.onChange(option && option.value ? option.value : '')
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label={ctIntl.formatMessage({ id: 'Home Geofence' })}
                        helperText={ctIntl.formatMessage({
                          id: fieldState.error?.message ?? '',
                        })}
                        error={!!fieldState.error}
                      />
                    )}
                  />
                )}
              />
              <TextFieldControlled
                ControllerProps={{
                  control,
                  name: 'tollingTagId',
                }}
                sx={{ width: '100%' }}
                label={ctIntl.formatMessage({ id: 'Tolling Tag Id' })}
              />
            </Stack>
          </Stack>
        </KarooFormStateContextProvider>
      )}
    </FormSection>
  )
}

export default GeneralSettings

const ColorIcon = styled('div')(({ color }: { color: string }) => ({
  borderRadius: '50%',
  cursor: 'pointer',
  display: 'inline-block',
  height: '15px',
  width: '15px',
  backgroundColor: `${color}80`,
  borderColor: `2px solid ${color}`,
  marginRight: '10px',
}))
