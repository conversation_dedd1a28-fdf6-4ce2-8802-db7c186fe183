import { useMutation } from '@tanstack/react-query'

import { makeMutationErrorHandlerWithSnackbar } from 'api/helpers'
import type { VehicleId } from 'api/types'
import { restPost } from 'src/api/rest-api-caller'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import type { JobInfo } from './useJobStatusQuery'

export type UploadJob = {
  id: number
  status: string
  fileName: string
  file: File
}

export declare namespace UploadVehicleDocument {
  type MutationInput = {
    vehicleId: VehicleId
    files: Array<File>
  }

  type ApiInput = {
    vehicleId: VehicleId
    file: File
    fileName: string
  }

  type ApiOutput = {
    request: {
      destinationPath: string
      sourceFile: string
      overwrite: boolean
    }
    job: JobInfo
  }
}

async function uploadVehicleDocument(
  params: UploadVehicleDocument.ApiInput,
): Promise<UploadVehicleDocument.ApiOutput> {
  const formData = new FormData()
  formData.append('SourceFile', params.file)
  formData.append(
    'TargetFile',
    `vehicles/${params.vehicleId}/documents/${params.fileName}`,
  )
  formData.append('Overwrite', 'true')

  return restPost<UploadVehicleDocument.ApiOutput>('/file/upload', formData, {
    upload: true,
  })
}

export default function useUploadVehicleDocumentMutation() {
  return useMutation({
    mutationFn: async ({ vehicleId, files }: UploadVehicleDocument.MutationInput) => {
      const jobs: Array<UploadJob> = []

      for (const file of files) {
        try {
          const result = await uploadVehicleDocument({
            vehicleId,
            file,
            fileName: file.name,
          })

          if (result.job.jobId) {
            jobs.push({
              id: result.job.jobId,
              status: result.job.status,
              fileName: file.name,
              file,
            })
          }
        } catch {
          enqueueSnackbarWithCloseAction(
            ctIntl.formatMessage({ id: 'File Upload Failed' }),
            { variant: 'error' },
          )
        }
      }

      return jobs
    },
    ...makeMutationErrorHandlerWithSnackbar({
      nonStandardErrorHandler() {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({ id: 'File Upload Failed' }),
          { variant: 'error' },
        )
      },
    }),
  })
}
