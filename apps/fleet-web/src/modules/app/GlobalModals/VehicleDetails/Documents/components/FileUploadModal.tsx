import { useCallback, useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  styled,
  Typography,
} from '@karoo-ui/core'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined'
import { useDropzone } from 'react-dropzone'

import type { VehicleId } from 'api/types'
import { ctIntl } from 'src/util-components/ctIntl'

import useUploadVehicleDocumentMutation, {
  type UploadJob,
} from '../api/useUploadVehicleDocumentMutation'
import {
  formatFileSize,
  getFileTypeIcon,
  getSupportedFileExtensions,
  isViewableFile,
  MAX_FILE_SIZE,
  SUPPORTED_FILE_TYPES,
} from '../utils/fileTypeUtils'
import UploadProgressSnackbar from './UploadProgressSnackbar'

type Props = {
  vehicleId: VehicleId
  onClose: () => void
}

const DropzoneContainer = styled(Box)<{
  isDragActive: boolean
}>(({ theme, isDragActive }) => ({
  border: `1px solid ${theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  textAlign: 'center',
  cursor: 'pointer',
  backgroundColor: isDragActive ? theme.palette.action.hover : 'transparent',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}))

export default function FileUploadModal({ vehicleId, onClose }: Props) {
  const [selectedFiles, setSelectedFiles] = useState<Array<File>>([])
  const [uploadJobs, setUploadJobs] = useState<Array<UploadJob>>([])
  const uploadMutation = useUploadVehicleDocumentMutation()

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } =
    useDropzone({
      accept: SUPPORTED_FILE_TYPES,
      maxSize: MAX_FILE_SIZE,
      multiple: true,
      onDrop: (acceptedFiles) => {
        setSelectedFiles((prev) => [...prev, ...acceptedFiles])
      },
      onDropRejected: (rejectedFiles) => {
        // Handle rejected files - could show error messages
        console.warn('Rejected files:', rejectedFiles)
      },
    })

  const supportedFormats = getSupportedFileExtensions()

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const handleJobComplete = useCallback(
    (jobId: number, _success: boolean) => {
      setUploadJobs((prev) => {
        const updatedJobs = prev.filter((job) => job.id !== jobId)

        // If this was the last job and all uploads are complete, close the modal
        if (updatedJobs.length === 0 && selectedFiles.length === 0) {
          onClose()
        }

        return updatedJobs
      })
    },
    [selectedFiles.length, onClose],
  )

  const handleJobClose = useCallback((jobId: number) => {
    setUploadJobs((prev) => prev.filter((job) => job.id !== jobId))
  }, [])

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return

    try {
      const jobs = await uploadMutation.mutateAsync({
        vehicleId,
        files: selectedFiles,
      })

      setUploadJobs(jobs)
      setSelectedFiles([])

      // If no jobs were created (immediate upload), close modal after a short delay
      if (jobs.length === 0) {
        onClose()
      }
    } catch (error) {
      // Error is handled by the mutation error handler
      console.error('Upload failed:', error)
    }
  }

  const handleCancel = () => {
    setSelectedFiles([])
    onClose()
  }

  const isUploading = uploadMutation.isPending

  console.log('uploadJobs', uploadJobs)

  return (
    <Dialog
      open
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>{ctIntl.formatMessage({ id: 'Upload Documents' })}</DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <DropzoneContainer
            {...getRootProps()}
            isDragActive={isDragActive}
          >
            <input {...getInputProps()} />
            <UploadFileIcon
              sx={{
                fontSize: 24,
                color: (() => {
                  if (isDragAccept) return 'success.main'
                  if (isDragReject) return 'error.main'
                  return 'action.active'
                })(),
              }}
            />
            <Typography gutterBottom>
              {ctIntl.formatMessage({ id: 'Click to upload or drag and drop' })}
            </Typography>
            <Button
              variant="outlined"
              sx={{ mb: 2 }}
            >
              {ctIntl.formatMessage({ id: 'BROWSE FILES' })}
            </Button>
            <Typography color="text.secondary">
              {ctIntl.formatMessage(
                // FIXME:
                { id: 'Supported file formats: {formats}' },
                { values: { formats: supportedFormats } },
              )}
            </Typography>
            <Typography color="text.secondary">
              {ctIntl.formatMessage({ id: '(max. 3MB)' })}
            </Typography>
          </DropzoneContainer>

          {selectedFiles.length > 0 && (
            <Stack
              spacing={1}
              sx={{ maxHeight: 200, overflowY: 'auto' }}
            >
              {selectedFiles.map((file, index) => (
                <Box
                  key={`${file.name}-${index}`}
                  sx={(theme) => ({
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: theme.spacing(1, 0),
                  })}
                >
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={2}
                  >
                    {getFileTypeIcon(file.type)}
                    <Box>
                      <Typography>{file.name}</Typography>
                      <Typography
                        variant="caption"
                        color="text.secondary"
                      >
                        {formatFileSize(file.size)}
                      </Typography>
                    </Box>
                  </Stack>
                  <Stack
                    direction="row"
                    spacing={1}
                  >
                    {/* TODO: Implement file preview and download */}
                    <IconButton size="small">
                      {isViewableFile(file.type) ? (
                        <VisibilityOutlinedIcon />
                      ) : (
                        <FileDownloadOutlinedIcon />
                      )}
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveFile(index)}
                      disabled={isUploading}
                    >
                      <DeleteOutlineOutlinedIcon />
                    </IconButton>
                  </Stack>
                </Box>
              ))}
            </Stack>
          )}
        </Stack>
      </DialogContent>

      <DialogActions
        sx={{
          p: 2,
          gap: 1,
          justifyContent: 'space-between',
          borderTop: '1px solid #e0e0e0',
        }}
      >
        <Button
          variant="outlined"
          color="secondary"
          onClick={handleCancel}
          disabled={isUploading}
        >
          {ctIntl.formatMessage({ id: 'Cancel' })}
        </Button>

        <Button
          variant="contained"
          onClick={handleUpload}
          disabled={selectedFiles.length === 0 || isUploading}
        >
          {isUploading
            ? ctIntl.formatMessage({ id: 'Uploading...' })
            : ctIntl.formatMessage({ id: 'Add File' })}
        </Button>
      </DialogActions>

      {/* Progress tracking snackbars for each upload job */}
      {uploadJobs.map((job) => (
        <UploadProgressSnackbar
          key={job.id}
          jobId={job.id}
          fileName={job.fileName}
          onComplete={(success) => handleJobComplete(job.id, success)}
          onClose={() => handleJobClose(job.id)}
        />
      ))}
    </Dialog>
  )
}
