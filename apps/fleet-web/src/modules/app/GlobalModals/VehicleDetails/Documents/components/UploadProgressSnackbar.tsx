import { useEffect } from 'react'
import { Box, LinearProgress, Typography } from '@karoo-ui/core'
import { useSnackbar } from 'notistack'
import { rgba } from 'polished'

import { ctIntl } from 'src/util-components/ctIntl'

import useJobStatusQuery from '../api/useJobStatusQuery'

type Props = {
  jobId: number
  fileName: string
  onComplete: (success: boolean) => void
  onClose: () => void
}

export default function UploadProgressSnackbar({
  jobId,
  fileName,
  onComplete,
  onClose,
}: Props) {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar()
  const jobStatusQuery = useJobStatusQuery(jobId)

  const jobInfo = jobStatusQuery.data
  const isCompleted = jobInfo?.status === 'Completed'
  const isFailed = jobInfo?.status === 'Failed'
  const isInProgress = jobInfo?.status === 'In Progress'
  const progress = jobInfo?.completionLevel || 0

  useEffect(() => {
    if (isCompleted) {
      onComplete(true)
      closeSnackbar()
      enqueueSnackbar(
        ctIntl.formatMessage(
          { id: 'File "{fileName}" uploaded successfully' },
          { values: { fileName } },
        ),
        { variant: 'success' },
      )
    } else if (isFailed) {
      onComplete(false)
      closeSnackbar()
      enqueueSnackbar(
        ctIntl.formatMessage(
          { id: 'Failed to upload file "{fileName}"' },
          { values: { fileName } },
        ),
        { variant: 'error' },
      )
    }
  }, [isCompleted, isFailed, fileName, onComplete, closeSnackbar, enqueueSnackbar])

  useEffect(() => {
    const snackbarId = enqueueSnackbar(
      <UploadProgressContent
        fileName={fileName}
        progress={progress}
        status={jobInfo?.status || 'Starting...'}
        isInProgress={isInProgress}
      />,
      {
        variant: 'info',
        persist: true,
        preventDuplicate: true,
        key: `upload-${jobId}`,
        action: (key) => (
          <button
            onClick={() => {
              closeSnackbar(key)
              onClose()
            }}
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
            }}
          >
            ✕
          </button>
        ),
      },
    )

    return () => {
      closeSnackbar(snackbarId)
    }
  }, [
    jobId,
    fileName,
    progress,
    jobInfo?.status,
    isInProgress,
    enqueueSnackbar,
    closeSnackbar,
    onClose,
  ])

  return null
}

type UploadProgressContentProps = {
  fileName: string
  progress: number
  status: string
  isInProgress: boolean
}

const UploadProgressContent = ({
  fileName,
  progress,
  status,
  isInProgress,
}: UploadProgressContentProps) => (
  <Box sx={{ minWidth: 300 }}>
    <Typography sx={{ mb: 1, color: 'white' }}>
      {ctIntl.formatMessage(
        { id: 'Uploading "{fileName}"...' },
        { values: { fileName } },
      )}
    </Typography>

    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress
          variant={isInProgress ? 'determinate' : 'indeterminate'}
          value={progress}
          sx={(theme) => ({
            height: 6,
            borderRadius: 3,
            backgroundColor: rgba(theme.palette.common.black, 0.3),
            '& .MuiLinearProgress-bar': {
              backgroundColor: 'white',
            },
          })}
        />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography sx={{ color: 'white' }}>
          {isInProgress ? `${Math.round(progress)}%` : '...'}
        </Typography>
      </Box>
    </Box>

    <Typography
      variant="caption"
      sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
    >
      {status}
    </Typography>
  </Box>
)
