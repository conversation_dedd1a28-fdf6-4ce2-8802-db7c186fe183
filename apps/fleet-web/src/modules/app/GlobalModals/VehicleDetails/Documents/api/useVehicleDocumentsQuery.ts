import { useQuery } from '@tanstack/react-query'

import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import { restGet } from 'api/rest-api-caller'
import type { VehicleId } from 'api/types'
import { extractFileNameFromUrl } from 'src/modules/carpool/components/ScdfIssuanceRequestDrawer/utils'
import { createQuery } from 'src/util-functions/react-query-utils'

export type VehicleDocument = {
  id: string
  fileName: string
  fileSize: number
  downloadUrl?: string
}

export declare namespace GetVehicleDocuments {
  type ApiInput = {
    vehicleId: VehicleId
  }

  type ApiOutput = {
    files: Array<string>
  }
}

const fetchVehicleDocuments = async (params: GetVehicleDocuments.ApiInput) => {
  const response = await restGet<GetVehicleDocuments.ApiOutput>(
    // FIXME: update the queries
    `/folder/getfiles?path=vehicles/${params.vehicleId}/documents&searchOption=AllDirectories&searchPattern=*`,
  )

  return response.files.map((file) => ({
    id: file,
    fileName: extractFileNameFromUrl(file),
    fileSize: 0, // FIXME:
    downloadUrl: file,
  }))
}

export const vehicleDocumentsQuery = (vehicleId: VehicleId) =>
  createQuery({
    queryKey: ['vehicle-documents', vehicleId],
    queryFn: () => fetchVehicleDocuments({ vehicleId }),
    ...makeQueryErrorHandlerWithToast(),
  })

export default function useVehicleDocumentsQuery(vehicleId: VehicleId) {
  return useQuery(vehicleDocumentsQuery(vehicleId))
}
