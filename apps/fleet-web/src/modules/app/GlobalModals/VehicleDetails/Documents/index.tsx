import { useC<PERSON>back, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  IconButton,
  LinearProgress,
  Stack,
  Typography,
  useDataGridColumnHelper,
  useGridApiRef,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'

import type { VehicleId } from 'api/types'
import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import useDeleteVehicleDocumentMutation from './api/useDeleteVehicleDocumentMutation'
import useVehicleDocumentsQuery, {
  type VehicleDocument,
} from './api/useVehicleDocumentsQuery'
import DeleteConfirmationModal from './components/DeleteConfirmationModal'
import FileUploadModal from './components/FileUploadModal'
import { formatFileSize, getFileTypeIcon } from './utils/fileTypeUtils'

type Props = {
  vehicleId: VehicleId
}

export default function Documents({ vehicleId }: Props) {
  const apiRef = useGridApiRef()
  const [uploadModalOpen, setUploadModalOpen] = useState(false)
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<VehicleDocument | null>(null)

  const documentsQuery = useVehicleDocumentsQuery(vehicleId)
  const deleteMutation = useDeleteVehicleDocumentMutation()

  const columnHelper = useDataGridColumnHelper<VehicleDocument>({
    filterMode: 'client',
  })

  const handleDelete = (document: VehicleDocument) => {
    setDocumentToDelete(document)
    setDeleteModalOpen(true)
  }

  const handleConfirmDelete = () => {
    if (!documentToDelete) return

    deleteMutation.mutate(
      {
        vehicleId,
        documentPath: documentToDelete.fileName, // FIXME:
        documentName: documentToDelete.fileName,
      },
      {
        onSuccess: () => {
          setDeleteModalOpen(false)
          setDocumentToDelete(null)
        },
      },
    )
  }

  const handleDownload = useCallback((document: VehicleDocument) => {}, [])

  const columns = useMemo(
    (): Array<GridColDef<VehicleDocument>> => [
      columnHelper.string((_, row) => row.fileName, {
        field: 'name',
        headerName: ctIntl.formatMessage({ id: 'Name' }),
        flex: 1,
        minWidth: 200,
        renderCell: ({ row }) => (
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
          >
            {/* {getFileTypeIcon(row.fileType)} */}
            <Typography noWrap>{row.fileName}</Typography>
          </Stack>
        ),
      }),
      {
        field: 'fileSize',
        headerName: ctIntl.formatMessage({ id: 'Size' }),
        width: 100,
        valueGetter: (_, row) => row.fileSize,
        valueFormatter: (value) => formatFileSize(value as number),
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        width: 120,
        getActions: ({ row }) => [
          <IconButton
            key="download"
            size="small"
            onClick={() => handleDownload(row)}
            title={ctIntl.formatMessage({ id: 'Download' })}
          >
            <FileDownloadOutlinedIcon fontSize="small" />
          </IconButton>,
          <IconButton
            key="delete"
            size="small"
            onClick={() => handleDelete(row)}
            title={ctIntl.formatMessage({ id: 'Delete' })}
            disabled={deleteMutation.isPending}
          >
            <DeleteOutlineOutlinedIcon fontSize="small" />
          </IconButton>,
        ],
      },
    ],
    [columnHelper, deleteMutation.isPending, handleDownload],
  )

  const documents = documentsQuery.data ?? []

  return (
    <Stack
      spacing={2}
      sx={{ height: '100%' }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={() => setUploadModalOpen(true)}
        >
          {ctIntl.formatMessage({ id: 'ADD FILE' })}
        </Button>
      </Box>

      <Box sx={{ flexGrow: 1, minHeight: 0 }}>
        <UserDataGridWithSavedSettingsOnIDB
          dataGridId={`vehicle-documents-${vehicleId}`}
          data-testid={`vehicle-documents-${vehicleId}`}
          Component={DataGrid}
          apiRef={apiRef}
          rows={documents}
          columns={columns}
          loading={documentsQuery.isFetching}
          pagination
          pageSizeOptions={[10, 25, 50]}
          rowReordering
          disableRowSelectionOnClick
          initialState={{
            pagination: { paginationModel: { pageSize: 10, page: 0 } },
          }}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            noRowsOverlay: () => (
              <DataStatePlaceholder
                label={ctIntl.formatMessage({ id: 'No documents available' })}
              />
            ),
          }}
          slotProps={{
            toolbar: KarooToolbar.createProps({
              slots: { searchFilter: { show: true } },
            }),
            pagination: { showFirstButton: true, showLastButton: true },
          }}
        />
      </Box>

      {uploadModalOpen && (
        <FileUploadModal
          vehicleId={vehicleId}
          onClose={() => setUploadModalOpen(false)}
        />
      )}

      {deleteModalOpen && (
        <DeleteConfirmationModal
          fileName={documentToDelete?.fileName ?? ''}
          onConfirm={handleConfirmDelete}
          onCancel={() => {
            setDeleteModalOpen(false)
            setDocumentToDelete(null)
          }}
          isDeleting={deleteMutation.isPending}
        />
      )}
    </Stack>
  )
}
