import { useMemo } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Autocomplete,
  CircularProgress,
  DatePicker,
  FormControlLabel,
  FormHelperText,
  KarooFormStateContextProvider,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import { TextFieldControlled, useControlledForm } from '@karoo-ui/core-rhf'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import type { QueryObserverSuccessResult } from '@tanstack/react-query'
import { DateTime } from 'luxon'
import { Controller, useWatch } from 'react-hook-form'
import { useHistory } from 'react-router-dom'
import { match } from 'ts-pattern'

import type { VehicleId } from 'api/types'
import useVehicleDetailsQuery, {
  type FetchVehicleDetailsQuery,
} from 'api/vehicles/useVehicleDetailsQuery'
import { getSettings_UNSAFE } from 'duxs/user'
import {
  getShowVehicleOverspeedThreshold,
  getUpdateVehicleOdometerSubuserPermission,
  getVehicleDetailsShowTerminalSerial,
  getVehiclesEditVehicleSetting,
} from 'duxs/user-sensitive-selectors'
import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import FormSection from 'src/components/_form/FormSection'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { useTypedSelector } from 'src/redux-hooks'

import { ctIntl } from 'cartrack-ui-kit'
import DescriptionsInput from './DescriptionsInput'
import IMaskNumberInput from './IMaskNumerInput'
import OverspeedThreshold from './OverspeedThreshold'
import {
  generateVehicleDetailsValidationSchema,
  registrationValidationSchema,
  type RegistrationValidationSchema,
  type VehicleDetailsSchema,
} from './schema'
import {
  useUpdateVehicleDetails,
  useUpdateVehicleRegistrationDetails,
} from './useUpdateVehicleDetailsMutation'

type ServiceType = 'Odo' | 'Date'

function VehicleDetailsSection({ vehicleId }: { vehicleId: VehicleId }) {
  const unitQuery = useVehicleDetailsQuery({ vehicleId })

  return match(unitQuery)
    .with({ status: 'error' }, () => null)
    .with({ status: 'pending' }, () => (
      <Stack
        sx={{
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          height: '100%',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data }) => (
      <VehicleDetailsSectionContent
        vehicleId={vehicleId}
        vehicleDetailsData={data}
      />
    ))
    .exhaustive()
}

const VehicleDetailsSectionContent = ({
  vehicleId,
  vehicleDetailsData,
}: {
  vehicleId: VehicleId
  vehicleDetailsData: QueryObserverSuccessResult<FetchVehicleDetailsQuery.Return>['data']
}) => {
  const history = useHistory()

  const updatevehicleDetailsMutation = useUpdateVehicleDetails()
  const updatevehicleRegistrationDetailsMutation = useUpdateVehicleRegistrationDetails()

  const { distanceInMiles, allowFullVehicleEditable } =
    useTypedSelector(getSettings_UNSAFE)

  const showVehicleOverspeedThreshold = useTypedSelector(
    getShowVehicleOverspeedThreshold,
  )
  const updateVehicleOdometerSubuserPermission = useTypedSelector(
    getUpdateVehicleOdometerSubuserPermission,
  )
  const vehiclesEditVehicleSetting = useTypedSelector(getVehiclesEditVehicleSetting)
  const vehicleDetailsShowTerminalSerial = useTypedSelector(
    getVehicleDetailsShowTerminalSerial,
  )

  const {
    fleetVehicle: vehicle,
    vehicleEngineTypes,
    vehicleFuelTypes,
  } = vehicleDetailsData

  const initialValues = {
    name: vehicle.name,
    descriptions: vehicle.descriptions.map((description) => ({
      value: description,
    })),
    odometer: vehicle.odometer ? String(vehicle.odometer) : '',
    terminalSerial: vehicle.terminalSerial,
    VIN: vehicle.VIN,
    engineNumber: vehicle.engineNumber,
    color: vehicle.color,
    year: vehicle.year,
    make: vehicle.make,
    model: vehicle.model,
    thirdPartyTerminalSerial: vehicle.thirdPartyTerminalSerial,
    fuelAverageConsumptionCombined: vehicle.fuelAverageConsumptionCombined,
    fuelTargetConsumptionCombined: vehicle.fuelTargetConsumptionCombined,
    engineTypeId: vehicle.engineTypeId ? String(vehicle.engineTypeId) : null,
    fuelTypeId: vehicle.fuelTypeId ? String(vehicle.fuelTypeId) : null,
  }

  const {
    control,
    formState,
    handleSubmit,
    reset,
    setValue: setFormValue,
  } = useControlledForm<VehicleDetailsSchema>({
    resolver: zodResolver(generateVehicleDetailsValidationSchema()),
    mode: 'all',
    defaultValues: initialValues,
  })

  const initialRegistrationValues = {
    registration: vehicle.registration,
    code: vehicle.code,
    licenceDates: {
      issueDate: vehicle.issueDate,
      expirationDate: vehicle.expirationDate,
    },
    nextServiceType: vehicle.nextServiceType,
    nextServiceDate: vehicle.nextServiceDate,
    nextServiceOdo: vehicle.nextServiceOdo,
  }

  const {
    control: registrationControl,
    formState: registrationFormState,
    handleSubmit: registrationHandleSubmit,
    reset: registrationReset,
    trigger,
  } = useControlledForm<RegistrationValidationSchema>({
    resolver: zodResolver(registrationValidationSchema),
    mode: 'all',
    defaultValues: initialRegistrationValues,
  })

  const watchedEngineType = useWatch({ name: 'engineTypeId', control })
  const watchedServiceType = useWatch({
    name: 'nextServiceType',
    control: registrationControl,
  })

  const vehicleEngineTypesOptions = useMemo(
    () =>
      vehicleEngineTypes.map((engineType) => ({
        label: engineType.description,
        value: engineType.vehicle_engine_type_id,
        has_fuel: engineType.has_fuel,
      })),
    [vehicleEngineTypes],
  )

  const vehicleFuelTypesOptions = useMemo(
    () =>
      vehicleFuelTypes.map((fuelType) => ({
        label: fuelType.description,
        value: fuelType.vehicle_fuel_type_id,
      })),
    [vehicleFuelTypes],
  )

  const hasFuel = useMemo(
    () =>
      vehicleEngineTypesOptions.find(
        (engineType) => String(engineType.value) === watchedEngineType,
      )?.has_fuel ?? false,
    [vehicleEngineTypesOptions, watchedEngineType],
  )

  const submitForm = handleSubmit((data) => {
    updatevehicleDetailsMutation.mutate(
      {
        vehicleId: String(vehicleId),
        data: {
          ...data,
          descriptions: data.descriptions.map((d) => d.value),
        },
      },
      {
        onSuccess: () => {
          reset(data)
        },
      },
    )
  })

  const submitRegistrationForm = registrationHandleSubmit((data) => {
    updatevehicleRegistrationDetailsMutation.mutate(
      {
        vehicleId: String(vehicleId),
        data,
      },
      {
        onSuccess: () => {
          registrationReset(data)
        },
      },
    )
  })

  const getSpecialFieldDisabledProps = () => {
    if (!allowFullVehicleEditable) {
      return { disabled: true }
    }
    return undefined
  }

  return (
    <Stack gap={2}>
      <FormSection
        label="General Info"
        mutationStatus={updatevehicleDetailsMutation.status}
        onSaveButtonClick={submitForm}
        onCancelButtonClick={reset}
        formState={formState}
        disabled={!vehiclesEditVehicleSetting}
      >
        {(isEditing) => {
          const readOnly = !isEditing
          return (
            <KarooFormStateContextProvider value={{ readOnly }}>
              <Stack gap={2}>
                <TextFieldControlled
                  ControllerProps={{ control, name: 'name' }}
                  sx={{ width: '100%' }}
                  label={ctIntl.formatMessage({ id: 'Name' })}
                />
                <DescriptionsInput
                  readOnly={readOnly}
                  formControl={control}
                />
                <Stack
                  direction={'row'}
                  gap={2}
                >
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'VIN',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'VIN' })}
                  />
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'engineNumber',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'Engine Number' })}
                  />
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'color',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'Color' })}
                  />
                </Stack>
                <Stack
                  direction={'row'}
                  gap={2}
                >
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'year',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'Year' })}
                  />
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'make',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'Make' })}
                  />
                  <TextFieldControlled
                    {...getSpecialFieldDisabledProps()}
                    ControllerProps={{
                      control,
                      name: 'model',
                    }}
                    sx={{ width: '100%' }}
                    label={ctIntl.formatMessage({ id: 'Model' })}
                  />
                </Stack>
                <Stack gap={0.5}>
                  <TextFieldControlled
                    disabled
                    ControllerProps={{
                      control,
                      name: 'odometer',
                    }}
                    sx={{ width: '100%' }}
                    label={`${ctIntl.formatMessage({ id: 'Odometer' })} (${
                      distanceInMiles ? 'mi' : 'km'
                    })`}
                  />
                  {updateVehicleOdometerSubuserPermission && (
                    <Stack
                      direction="row"
                      pl={2}
                      gap={0.5}
                    >
                      <Tooltip
                        title={ctIntl.formatMessage({
                          id: 'vehicleDetails.odometer.jumpButton.tooltip',
                        })}
                      >
                        <FormHelperText
                          sx={(theme) => ({
                            cursor: 'pointer',
                            color: theme.palette.info.main,
                            '&:hover': {
                              textDecoration: 'underline',
                            },
                          })}
                          onClick={() => {
                            history.push(
                              getVehicleDetailsModalMainPath(
                                history.location,
                                vehicleId,
                                'ODOMETER',
                              ),
                            )
                          }}
                        >
                          {`${ctIntl.formatMessage({
                            id: 'vehicleDetails.viewOrEditOdometer.link',
                          })}.`}
                        </FormHelperText>
                      </Tooltip>
                      <FormHelperText>
                        {ctIntl.formatMessage({
                          id: 'vehicleDetails.viewOrEditOdometer.moreInfo',
                        })}
                      </FormHelperText>
                      <WarningAmberOutlinedIcon
                        sx={(theme) => ({
                          width: '20px',
                          color: theme.palette.warning.main,
                        })}
                      />
                    </Stack>
                  )}
                </Stack>
                {vehicleDetailsShowTerminalSerial && (
                  <Stack
                    direction={'row'}
                    gap={2}
                  >
                    <TextFieldControlled
                      disabled
                      ControllerProps={{
                        control,
                        name: 'terminalSerial',
                      }}
                      sx={{ width: '100%' }}
                      label={ctIntl.formatMessage({ id: 'Terminal Serial' })}
                    />
                    <TextFieldControlled
                      disabled
                      ControllerProps={{
                        control,
                        name: 'thirdPartyTerminalSerial',
                      }}
                      sx={{ width: '100%' }}
                      label={ctIntl.formatMessage({ id: 'Linked Terminal Serial' })}
                    />
                  </Stack>
                )}
                <Stack
                  direction={'row'}
                  gap={2}
                >
                  <Controller
                    name="fuelAverageConsumptionCombined"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        label={ctIntl.formatMessage({
                          id: 'Average Consumption Combined',
                        })}
                        name={field.name}
                        fullWidth
                        value={field.value}
                        size="small"
                        onChange={(event) => field.onChange(event?.target.value)}
                        slotProps={{
                          input: { inputComponent: IMaskNumberInput as any },
                        }}
                      />
                    )}
                  />
                  <Controller
                    name="fuelTargetConsumptionCombined"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        label={ctIntl.formatMessage({
                          id: 'Target Consumption Combined',
                        })}
                        name={field.name}
                        fullWidth
                        value={field.value}
                        size="small"
                        onChange={(event) => field.onChange(event?.target.value)}
                        slotProps={{
                          input: { inputComponent: IMaskNumberInput as any },
                        }}
                      />
                    )}
                  />
                </Stack>
                <Stack
                  direction={'row'}
                  gap={2}
                >
                  <Controller
                    control={control}
                    name="engineTypeId"
                    render={({ field }) => (
                      <Autocomplete
                        sx={{ width: '100%' }}
                        {...getAutocompleteVirtualizedProps({
                          options: vehicleEngineTypesOptions || [],
                        })}
                        value={
                          vehicleEngineTypesOptions.find(
                            (type) => String(type.value) === String(field.value),
                          ) || null
                        }
                        onChange={(_event, option) => {
                          field.onChange(
                            option && option.value ? String(option.value) : null,
                          )
                          setFormValue('fuelTypeId', null)
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label={ctIntl.formatMessage({ id: 'Engine Type' })}
                          />
                        )}
                      />
                    )}
                  />
                  {hasFuel && (
                    <Controller
                      control={control}
                      name="fuelTypeId"
                      render={({ field }) => (
                        <Autocomplete
                          sx={{ width: '100%' }}
                          {...getAutocompleteVirtualizedProps({
                            options: vehicleFuelTypesOptions || [],
                          })}
                          value={
                            vehicleFuelTypesOptions.find(
                              (type) => String(type.value) === String(field.value),
                            ) || null
                          }
                          onChange={(_event, option) => {
                            field.onChange(
                              option && option.value ? String(option.value) : '',
                            )
                          }}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label={ctIntl.formatMessage({ id: 'Fuel Type' })}
                            />
                          )}
                        />
                      )}
                    />
                  )}
                </Stack>
              </Stack>
            </KarooFormStateContextProvider>
          )
        }}
      </FormSection>
      {showVehicleOverspeedThreshold && (
        <OverspeedThreshold vehicleDetailsData={vehicleDetailsData} />
      )}

      <FormSection
        label="Registration"
        mutationStatus={updatevehicleRegistrationDetailsMutation.status}
        onSaveButtonClick={submitRegistrationForm}
        onCancelButtonClick={registrationReset}
        formState={registrationFormState}
        disabled={!vehiclesEditVehicleSetting}
      >
        {(isEditing) => (
          <KarooFormStateContextProvider value={{ readOnly: !isEditing }}>
            <Stack gap={2}>
              <Stack
                direction={'row'}
                gap={2}
              >
                <TextFieldControlled
                  disabled
                  ControllerProps={{
                    control: registrationControl,
                    name: 'registration',
                  }}
                  sx={{ width: '100%' }}
                  label={ctIntl.formatMessage({ id: 'Registration' })}
                />
                <TextFieldControlled
                  readOnly={!isEditing}
                  ControllerProps={{
                    control: registrationControl,
                    name: 'code',
                  }}
                  sx={{ width: '100%' }}
                  label={ctIntl.formatMessage({ id: 'Code' })}
                />
              </Stack>
              <Stack
                direction={'row'}
                gap={2}
              >
                <Controller
                  name="licenceDates.issueDate"
                  control={registrationControl}
                  render={({ field, fieldState }) => (
                    <DatePicker
                      label={ctIntl.formatMessage({ id: 'Registration Date' })}
                      value={field.value ? DateTime.fromMillis(field.value) : null}
                      onChange={(newValue) => {
                        field.onChange(newValue?.toMillis() ?? null)
                        trigger([
                          'licenceDates.issueDate',
                          'licenceDates.expirationDate',
                        ])
                      }}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!fieldState.error,
                          helperText: fieldState.error?.message,
                        },
                      }}
                    />
                  )}
                />
                <Controller
                  name="licenceDates.expirationDate"
                  control={registrationControl}
                  render={({ field, fieldState }) => (
                    <DatePicker
                      label={ctIntl.formatMessage({ id: 'COE Expiration Date' })}
                      value={field.value ? DateTime.fromMillis(field.value) : null}
                      onChange={(newValue) => {
                        field.onChange(newValue?.toMillis() ?? null)
                        trigger([
                          'licenceDates.issueDate',
                          'licenceDates.expirationDate',
                        ])
                      }}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!fieldState.error,
                          helperText: fieldState.error?.message,
                        },
                      }}
                    />
                  )}
                />
              </Stack>
              <Typography variant="body2">
                {ctIntl.formatMessage({
                  id: 'Next service type',
                })}
              </Typography>
              <Stack
                direction={'row'}
                alignItems={watchedServiceType === 'Date' ? 'flex-end' : 'flex-start'}
                gap={2}
              >
                <Controller
                  control={registrationControl}
                  name="nextServiceType"
                  render={({ field }) => (
                    <RadioGroup
                      value={field.value}
                      sx={(theme) => ({
                        '& .MuiRadio-root': {
                          ...(isEditing ? {} : { color: theme.palette.grey[400] }),
                          '&.Mui-checked': {
                            color: isEditing
                              ? theme.palette.primary.main
                              : theme.palette.primary.light,
                          },
                        },
                      })}
                      onClick={(event) => {
                        const target = event.target as HTMLButtonElement
                        const nextValue = target.value as ServiceType

                        if (nextValue !== undefined) {
                          field.onChange(
                            watchedServiceType === nextValue ? null : nextValue,
                          )
                        }
                      }}
                    >
                      <FormControlLabel
                        key={'Odo'}
                        value={'Odo'}
                        control={<Radio size={'small'} />}
                        label={ctIntl.formatMessage({ id: 'Odometer' })}
                      />
                      <FormControlLabel
                        key={'Date'}
                        value={'Date'}
                        control={<Radio size={'small'} />}
                        label={ctIntl.formatMessage({ id: 'Date' })}
                      />
                    </RadioGroup>
                  )}
                />
                {watchedServiceType === 'Date' && (
                  <Controller
                    name="nextServiceDate"
                    control={registrationControl}
                    render={({ field }) => (
                      <DatePicker
                        label={ctIntl.formatMessage({
                          id: 'Next Service Date',
                        })}
                        value={field.value ? DateTime.fromMillis(field.value) : null}
                        onChange={(newValue) => {
                          field.onChange(newValue?.toMillis() ?? null)
                        }}
                      />
                    )}
                  />
                )}
                {watchedServiceType === 'Odo' && (
                  <TextFieldControlled
                    slotProps={{
                      input: { inputMode: 'numeric' },
                    }}
                    ControllerProps={{
                      control: registrationControl,
                      name: 'nextServiceOdo',
                    }}
                    label={distanceInMiles ? 'mi' : 'kms'}
                    onChange={({ target: { value } }, { controller }) => {
                      const trimmedValue = value.replaceAll(/\s/g, '')

                      // Accept 7 digits at most
                      // eslint-disable-next-line sonarjs/concise-regex
                      if (/^[0-9]{0,7}$/.test(trimmedValue)) {
                        const numberLimit = Number(trimmedValue)
                        controller.onChange(numberLimit)
                      }
                    }}
                  />
                )}
              </Stack>
            </Stack>
          </KarooFormStateContextProvider>
        )}
      </FormSection>
    </Stack>
  )
}

export default VehicleDetailsSection
