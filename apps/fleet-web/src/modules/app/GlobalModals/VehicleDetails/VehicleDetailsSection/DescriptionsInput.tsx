import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from '@karoo-ui/core'
import { TextFieldControlled } from '@karoo-ui/core-rhf'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import { useFieldArray, type Control } from 'react-hook-form'

import { ctIntl } from 'cartrack-ui-kit'
import type { VehicleDetailsSchema } from './schema'

type Props = {
  readOnly: boolean
  formControl: Control<VehicleDetailsSchema>
}

const MaxNumberOfDescriptions = 3

const DescriptionsInput = ({ readOnly, formControl }: Props) => {
  const { fields, append, remove } = useFieldArray({
    control: formControl,
    name: 'descriptions',
  })

  return (
    <>
      {fields.map((field, index) => (
        <Description
          key={field.id}
          readOnly={readOnly}
          formControl={formControl}
          index={index}
          hideDeleteButton={fields.length === 1}
          onDelete={() => remove(index)}
        />
      ))}
      {fields.length < MaxNumberOfDescriptions && !readOnly && (
        <Button
          sx={{ width: 'fit-content' }}
          startIcon={<AddIcon />}
          onClick={() => append({ value: '' })}
        >
          {ctIntl.formatMessage({
            id: 'Add more description',
          })}
        </Button>
      )}
    </>
  )
}

export default DescriptionsInput

type DescriptionProps = {
  readOnly: boolean
  formControl: Control<VehicleDetailsSchema>
  index: number
  hideDeleteButton: boolean
  onDelete: () => void
}

const Description = ({
  index,
  readOnly,
  formControl,
  hideDeleteButton,
  onDelete,
}: DescriptionProps) => (
  <Stack
    direction="row"
    gap={1}
  >
    <TextFieldControlled
      readOnly={readOnly}
      ControllerProps={{ control: formControl, name: `descriptions.${index}.value` }}
      sx={{ width: '100%' }}
      label={`${ctIntl.formatMessage({ id: 'Description' })}${
        index === 0 ? '' : ` ${index + 1}`
      }`}
    />
    {!readOnly && !hideDeleteButton && (
      <Tooltip
        title={ctIntl.formatMessage({
          id: 'Delete',
        })}
      >
        <IconButton
          aria-label="delete"
          onClick={onDelete}
        >
          <DeleteOutlinedIcon color="error" />
        </IconButton>
      </Tooltip>
    )}
  </Stack>
)
